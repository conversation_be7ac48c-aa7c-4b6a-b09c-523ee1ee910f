<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title> Trading Bot Dashboard</title>
    <!-- TradingView Charting Library -->
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/awesomplete/1.1.5/awesomplete.min.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/awesomplete/1.1.5/awesomplete.min.js"></script>
  <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .current-price {
            font-size: 1.8em;
            margin: 10px 0;
            color: #00ff88;
        }

        .price-change {
            font-size: 1.2em;
            margin: 5px 0;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .control-group {
            background: rgba(255, 255, 255, 0.08);
            padding: 18px 22px;
            border-radius: 12px;
            border: 1px solid rgba(0, 212, 255, 0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .control-group:hover {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-size: 0.95em;
            color: #00d4ff;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        select, button {
            background: rgba(15, 15, 35, 0.95);
            color: #ffffff;
            border: 2px solid rgba(0, 212, 255, 0.5);
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        select:hover, button:hover {
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.15);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        select:focus {
            outline: none;
            border-color: #00ff88;
            background: rgba(15, 15, 35, 1);
            box-shadow: 0 0 15px rgba(0, 255, 136, 0.4);
        }

        /* Dropdown options styling */
        select option {
            background: #1a1a2e;
            color: #ffffff;
            padding: 8px 12px;
            border: none;

        select optgroup {
            background: #0f0f23;
            color: #00d4ff;
            font-weight: bold;
            font-style: normal;
            padding: 4px 0;
        }

        select option:hover,
        select option:focus,
        select option:checked {
            background: rgba(0, 212, 255, 0.8) !important;
            color: #ffffff !important;
        }

        /* Enhanced button styling */
        button:active {
            transform: scale(0.98);
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        button:active {
            transform: scale(0.98);
        }

        .dashboard {
            display: grid;
            grid-template-columns: 3fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
            min-height: 600px;

        .chart-container {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chart-container h3 {
            margin-bottom: 20px;
            text-align: center;
            color: #00d4ff;
        }

        #tradingview_chart {
            height: 500px;
            width: 100%;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.2);
        }

        .chart-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .chart-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 6px 12px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85em;
        }

        .chart-btn:hover, .chart-btn.active {
            background: rgba(0, 212, 255, 0.2);
            border-color: #00d4ff;
        }

        .signals-panel {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            overflow-y: auto;
        }

        .signal-box {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #666;
            transition: all 0.3s ease;
        }

        .signal-box.buy {
            border-left-color: #00ff88;
            background: rgba(0, 255, 136, 0.1);
            animation: pulse-green 2s infinite;
        }

        .signal-box.sell {
            border-left-color: #ff4757;
            background: rgba(255, 71, 87, 0.1);
            animation: pulse-red 2s infinite;
        }

        @keyframes pulse-green {
            0%, 100% { box-shadow: 0 0 5px rgba(0, 255, 136, 0.3); }
            50% { box-shadow: 0 0 20px rgba(0, 255, 136, 0.6); }
        }

        @keyframes pulse-red {
            0%, 100% { box-shadow: 0 0 5px rgba(255, 71, 87, 0.3); }
            50% { box-shadow: 0 0 20px rgba(255, 71, 87, 0.6); }
        }

        .signal-title {
            font-size: 1.3em;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .signal-conditions {
            list-style: none;
        }

        .signal-conditions li {
            padding: 8px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }

        .status-met {
            background-color: #00ff88;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        .status-not-met {
            background-color: #ff4757;
            box-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
        }

        .current-signal-box {
            text-align: center;
            margin-top: 20px;
            padding: 20px;
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .current-signal-box.buy-active {
            border-color: #00ff88;
            background: rgba(0, 255, 136, 0.1);
        }

        .current-signal-box.sell-active {
            border-color: #ff4757;
            background: rgba(255, 71, 87, 0.1);
        }

        .signal-strength {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            background: rgba(0,0,0,0.2);
        }

        .strength-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .strength-fill {
            height: 100%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
        }

        .metric-value {
            font-size: 1.8em;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .metric-label {
            color: #cccccc;
            font-size: 0.9em;
        }

        .alert-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .alert-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .alert-time {
            font-size: 0.8em;
            color: #888;
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #00d4ff;
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            animation: slideIn 0.3s ease;
            max-width: 300px;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        @media (max-width: 1200px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
        }

        /* Search styles */
        .search-container {
            position: relative;
            width: 250px;
        }

        .search-container input {
            width: 100%;
            padding: 8px 12px;
            background: rgba(15, 15, 35, 0.95);
            border: 2px solid rgba(0, 255, 136, 0.3);
            border-radius: 6px;
            color: #fff;
            font-size: 0.95em;
            transition: all 0.3s ease;
        }

        .search-container input:focus {
            outline: none;
            border-color: rgba(0, 255, 136, 0.6);
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.2);
        }

        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: rgba(15, 15, 35, 0.98);
            border: 1px solid rgba(0, 255, 136, 0.2);
            border-radius: 8px;
            margin-top: 5px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            display: none;
        }

        .search-results.active {
            display: block;
        }

        .search-result-item {
            padding: 12px 15px;
            cursor: pointer;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            font-size: 0.95em;
            color: #00ff88;
            transition: all 0.2s ease;
        }

        .search-result-item:hover {
            background: rgba(0, 255, 136, 0.1);
        }

        .search-result-item .symbol-name {
            font-weight: bold;
            color: #00d4ff;
            font-size: 1.1em;
        }

        .search-result-item .company-name {
            color: #ffffff;
            font-size: 0.9em;
            margin-top: 2px;
        }

        .search-result-item .exchange-tag {
            display: inline-block;
            background: rgba(0, 212, 255, 0.2);
            color: #00d4ff;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8em;
            margin-top: 4px;
            margin-right: 5px;
        }

        .search-result-item .type-tag {
            display: inline-block;
            background: rgba(0, 255, 136, 0.2);
            color: #00ff88;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8em;
            margin-top: 4px;
        }

        .search-result-item.not-met {
            color: #666;
        }

        /* Loading spinner */
        .loading-spinner {
            width: 30px;
            height: 30px;
            border: 3px solid rgba(0, 212, 255, 0.1);
            border-radius: 50%;
            border-top: 3px solid #00d4ff;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        .loading-text {
            text-align: center;
            color: #00d4ff;
            margin: 10px 0;
            font-size: 0.9em;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .header h1 {
                font-size: 2em;
            }

            .search-container {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈  Trading Bot Dashboard</h1>
            <div class="current-price" id="currentPrice">Loading  Price...</div>
            <div class="price-change" id="priceChange"></div>
            <div id="lastUpdate"></div>
        </div>

        <div class="controls">
          <div class="control-group">
            <label for="symbolSearch">Search Symbol:</label>
            <div class="search-container">
                <input type="text" id="symbolSearch" class="awesomplete" placeholder="Type symbol (e.g. AAPL)" style="width: 100%; margin-bottom: 8px;">
                <div id="searchResults" class="search-results"></div>
            </div>
          </div>
            <div class="control-group">
                <label for="timeframe">Timeframe:</label>
                <select id="timeframe">
                    <option value="2">2 Minutes</option>
                    <option value="60">1 Hour</option>
                    <option value="240">4 Hours</option>
                    <option value="D" selected>1 Day</option>
                </select>
            </div>
            <div class="control-group">
                <button onclick="refreshData()" id="refreshBtn">🔄 Refresh Data</button>
            </div>
            <div class="control-group">
                <button onclick="toggleAlerts()" id="alertBtn">🔔 Enable Alerts</button>
            </div>
        </div>

        <div class="dashboard">
            <div class="chart-container">
                <h3 id="chartTitle">Select a Symbol to View Chart</h3>

                <div id="tradingview_chart">
                    <div class="empty-state">
                        <div style="text-align: center; padding: 40px; color: #888;">
                            <div style="font-size: 1.2em; margin-bottom: 10px;">📊 No Symbol Selected</div>
                            <div style="font-size: 0.9em;">Use the search box above to select a symbol</div>
                        </div>
                    </div>
                </div>

                <!-- TTM Squeeze Chart (Pine Script Style) -->
                <div class="ttm-squeeze-container" style="margin-top: 20px; background: rgba(255, 255, 255, 0.05); border-radius: 10px; padding: 20px;">
                    <h4 style="color: #00d4ff; text-align: center; margin-bottom: 15px;">📊 TTM Squeeze Indicator (Pine Script Logic)</h4>
                    <canvas id="ttmSqueezeChart" width="900" height="300" style="width: 100%; height: 300px; background: rgba(0, 0, 0, 0.4); border-radius: 5px; border: 1px solid rgba(255, 255, 255, 0.1);"></canvas>
                    <div class="ttm-info" style="display: flex; justify-content: space-between; margin-top: 15px; font-size: 11px; color: #ccc;">
                        <div>
                            <strong>Parameters:</strong> Length=20, BB Mult=2.0, KC Mult=1.5, Mom Length=12
                        </div>
                        <div>
                            <strong>Legend:</strong>
                            <span style="color: #F44336;">🔴 Squeeze ON</span> |
                            <span style="color: #4CAF50;">🟢 Squeeze OFF</span> |
                            <span style="color: #9E9E9E;">⚪ No Signal</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="signals-panel">
                <h3 style="text-align: center; margin-bottom: 25px; color: #00d4ff;">🎯 Trading Signals</h3>
                
                <div class="signal-box" id="buySignal">
                    <div class="signal-title" style="color: #00ff88;">🟢 BUY SIGNAL</div>
                    <ul class="signal-conditions">
                        <li>
                            Price > 50 EMA + 1%
                            <span class="status-indicator" id="buyCondition1"></span>
                        </li>
                        <li>
                            TTM Squeeze: Yellow/Red
                            <span class="status-indicator" id="buyCondition2"></span>
                        </li>
                        <li>
                            RSI > 50
                            <span class="status-indicator" id="buyCondition3"></span>
                        </li>
                        <li>
                            Price > BB Upper
                            <span class="status-indicator" id="buyCondition4"></span>
                        </li>
                    </ul>
                </div>

                <div class="signal-box" id="sellSignal">
                    <div class="signal-title" style="color: #ff4757;">🔴 SELL SIGNAL</div>
                    <ul class="signal-conditions">
                        <li>
                            Price < 50 EMA - 1%
                            <span class="status-indicator" id="sellCondition1"></span>
                        </li>
                        <li>
                            TTM Squeeze: Blue
                            <span class="status-indicator" id="sellCondition2"></span>
                        </li>
                        <li>
                            RSI < 50
                            <span class="status-indicator" id="sellCondition3"></span>
                        </li>
                        <li>
                            Price < BB Lower
                            <span class="status-indicator" id="sellCondition4"></span>
                        </li>
                    </ul>
                </div>

                <div class="current-signal-box" id="currentSignalBox">
                    <div style="font-size: 1.2em; margin-bottom: 10px;">Current Signal:</div>
                    <div id="currentSignal" style="font-size: 1.5em; font-weight: bold;">ANALYZING...</div>
                    
                    <div class="signal-strength">
                        <div>Signal Strength:</div>
                        <div class="strength-bar">
                            <div class="strength-fill" id="strengthFill" style="width: 0%; background: #666;"></div>
                        </div>
                        <div id="strengthText" style="margin-top: 5px; font-size: 0.9em;">0%</div>
                    </div>
                </div>

                <div class="alert-section">
                    <h4 style="margin-bottom: 15px; color: #00d4ff;">🚨 Recent Alerts</h4>
                    <div id="alertList">
                        <div style="text-align: center; color: #888; font-size: 0.9em;">No alerts yet</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value" id="rsiValue">--</div>
                <div class="metric-label">RSI (14)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="emaValue">--</div>
                <div class="metric-label">50-day EMA</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="volumeValue">--</div>
                <div class="metric-label">Volume</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="changeValue">--</div>
                <div class="metric-label">Daily Change</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="ttmValue">--</div>
                <div class="metric-label">TTM Squeeze</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="signalCount">0</div>
                <div class="metric-label">Signals Today</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="apiCallsCount">0/8</div>
                <div class="metric-label">API Calls/Min</div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let widget;
        let currentData = {};
        let alertsEnabled = false;
        let signalHistory = [];
        let lastSignal = null;
        let activeIndicators = ['ema'];
        let currentSymbol = null;
        let searchTimeout;

        // Initialize search functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM Content Loaded - Initializing search...');

            const searchInput = document.getElementById('symbolSearch');
            const searchResults = document.getElementById('searchResults');

            // Verify elements exist
            if (!searchInput) {
                console.error('❌ symbolSearch input not found!');
                return;
            }
            if (!searchResults) {
                console.error('❌ searchResults div not found!');
                return;
            }

            console.log('✅ Search elements found successfully');

            // Initialize empty states
            initializeEmptyStates();

            // Test search functionality immediately
            setTimeout(() => {
                console.log('🧪 Testing search functionality...');
                searchSymbolsFromTwelveData('AA').then(results => {
                    console.log(`🧪 Test search returned ${results.length} results:`, results);
                }).catch(error => {
                    console.error('🧪 Test search failed:', error);
                });
            }, 1000);

            searchInput.addEventListener('input', function() {
                console.log(`⌨️ User typed: "${this.value}"`);

                clearTimeout(searchTimeout);
                const query = this.value.trim();

                if (query.length < 1) {
                    console.log('❌ Query too short, hiding results');
                    searchResults.classList.remove('active');
                    return;
                }

                console.log(`🔍 Starting search for: "${query}"`);

                searchTimeout = setTimeout(async () => {
                    try {
                        console.log(`⏰ Search timeout triggered for: "${query}"`);

                        // Use Twelve Data symbol search API
                        const symbols = await searchSymbolsFromTwelveData(query);
                        console.log(`📊 Search returned ${symbols.length} symbols`);

                        displaySearchResults(symbols);

                    } catch (error) {
                        console.error('🚨 Error in search timeout:', error);

                        // Emergency fallback
                        const fallbackSymbols = getFallbackSymbols().filter(s => {
                            const symbolMatch = s.symbol.toUpperCase().includes(query.toUpperCase());
                            const nameMatch = s.name.toUpperCase().includes(query.toUpperCase());
                            return symbolMatch || nameMatch;
                        }).slice(0, 10);

                        console.log(`🔄 Using ${fallbackSymbols.length} fallback symbols`);
                        displaySearchResults(fallbackSymbols);
                    }
                }, 300);
            });

            // Close search results when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.search-container')) {
                    searchResults.classList.remove('active');
                }
            });
        });

        function displaySearchResults(results) {
            const searchResults = document.getElementById('searchResults');
            console.log(`📋 Displaying ${results.length} search results`);

            if (!searchResults) {
                console.error('❌ searchResults element not found!');
                return;
            }

            if (results.length === 0) {
                searchResults.innerHTML = '<div class="search-result-item" style="color: #888;">No matches found</div>';
                console.log('⚠️ No results to display');
            } else {
                searchResults.innerHTML = results.map(item => `
                    <div class="search-result-item" data-symbol="${item.symbol}" data-exchange="${item.exchange}">
                        <div class="symbol-name">${item.symbol}</div>
                        <div class="company-name">${item.name}</div>
                        <div class="exchange-tag">${item.exchange}</div>
                        <div class="type-tag">${item.type || 'Stock'}</div>
                    </div>
                `).join('');

                console.log(`✅ Generated HTML for ${results.length} results`);

                // Add click handlers to results
                searchResults.querySelectorAll('.search-result-item').forEach((item, index) => {
                    item.addEventListener('click', function() {
                        const symbolName = this.dataset.symbol;
                        const exchange = this.dataset.exchange;

                        console.log(`🎯 User selected: ${symbolName} from ${exchange}`);

                        // Use clean symbol for API calls
                        selectSymbol(symbolName);
                        searchResults.classList.remove('active');
                    });
                    console.log(`✅ Added click handler for result ${index + 1}: ${item.dataset.symbol}`);
                });
            }

            // Always show the dropdown
            searchResults.classList.add('active');
            console.log('✅ Search results dropdown activated');
        }

        function selectSymbol(symbol) {
            if (!symbol) {
                console.error('No symbol provided to selectSymbol');
                return;
            }

            console.log(`🎯 Selecting symbol: ${symbol}`);

            // Show loading states immediately
            showLoadingStates();

            // Clean the symbol for API usage
            let cleanSymbol = symbol;
            let displaySymbol = symbol;

            // Handle different symbol formats
            if (symbol.includes(':')) {
                const parts = symbol.split(':');
                cleanSymbol = parts[1]; // Use the symbol part after the colon
                displaySymbol = symbol; // Keep full format for display
            } else if (symbol.includes('/')) {
                // Crypto pairs like BTC/USD - use as-is
                cleanSymbol = symbol;
                displaySymbol = symbol;
            } else {
                // Regular stocks - use as-is
                cleanSymbol = symbol;
                displaySymbol = `NASDAQ:${symbol}`; // Default to NASDAQ for display
            }

            // Set global current symbol for API calls
            currentSymbol = cleanSymbol;

            // Update UI elements
            document.getElementById('symbolSearch').value = cleanSymbol;
            document.getElementById('chartTitle').textContent = `${displaySymbol} - TradingView Professional Chart`;
            document.getElementById('searchResults').classList.remove('active');

            // Update current price display immediately
            document.getElementById('currentPrice').textContent = `${cleanSymbol}: Loading...`;

            console.log(`✅ Symbol set - Clean: ${cleanSymbol}, Display: ${displaySymbol}, Current: ${currentSymbol}`);

            // Initialize chart with display symbol
            initTradingViewWidget(displaySymbol);

            // Fetch technical indicators using clean symbol
            fetchTechnicalIndicators(cleanSymbol).catch(error => {
                console.error('Error fetching technical indicators:', error);
                showToast(`Failed to load data for ${cleanSymbol}`, 'error');
            });
        }

        function showLoadingStates() {
            // Show loading state in signals panel
            document.getElementById('buySignal').innerHTML = `
                <div class="signal-container">
                    <div class="signal-header" style="color: #00ff88;">
                        <span class="signal-icon">⬆️</span> BUY CONDITIONS
                    </div>
                    <div class="loading-spinner"></div>
                    <div class="loading-text">Analyzing market data...</div>
                </div>
            `;

            document.getElementById('sellSignal').innerHTML = `
                <div class="signal-container">
                    <div class="signal-header" style="color: #ff4757;">
                        <span class="signal-icon">⬇️</span> SELL CONDITIONS
                    </div>
                    <div class="loading-spinner"></div>
                    <div class="loading-text">Analyzing market data...</div>
                </div>
            `;

            // Show loading in other indicators
            document.getElementById('rsiValue').textContent = 'Loading...';
            document.getElementById('emaValue').textContent = 'Loading...';
            document.getElementById('volumeValue').textContent = 'Loading...';
            document.getElementById('changeValue').textContent = 'Loading...';
            document.getElementById('ttmValue').textContent = 'Loading...';
        }

        function updateSignals(conditions = []) {
            const buySignalElement = document.getElementById('buySignal');
            const sellSignalElement = document.getElementById('sellSignal');

            const buyConditions = conditions.filter(c => c.type === 'buy');
            const sellConditions = conditions.filter(c => c.type === 'sell');

            // Update buy signal
            buySignalElement.innerHTML = `
                <div class="signal-container">
                    <div class="signal-header" style="color: #00ff88;">
                        <span class="signal-icon">⬆️</span> BUY CONDITIONS
                    </div>
                    <div class="conditions-list">
                        ${buyConditions.map(c => `
                            <div class="condition-item ${c.met ? 'met' : 'not-met'}">
                                ${c.met ? '✅' : '❌'} ${c.description}
                            </div>
                        `).join('')}
                    </div>
                    <div class="signal-summary">
                        ${buyConditions.filter(c => c.met).length}/${buyConditions.length} conditions met
                    </div>
                </div>
            `;

            // Update sell signal
            sellSignalElement.innerHTML = `
                <div class="signal-container">
                    <div class="signal-header" style="color: #ff4757;">
                        <span class="signal-icon">⬇️</span> SELL CONDITIONS
                    </div>
                    <div class="conditions-list">
                        ${sellConditions.map(c => `
                            <div class="condition-item ${c.met ? 'met' : 'not-met'}">
                                ${c.met ? '✅' : '❌'} ${c.description}
                            </div>
                        `).join('')}
                    </div>
                    <div class="signal-summary">
                        ${sellConditions.filter(c => c.met).length}/${sellConditions.length} conditions met
                    </div>
                </div>
            `;
        }

            // Show loading in TTM squeeze
            document.querySelector('.ttm-squeeze-container').innerHTML = `
                <h4 style="color: #00d4ff; text-align: center; margin-bottom: 15px;">📊 TTM Squeeze Indicator</h4>
                <div class="loading-spinner"></div>
                <div class="loading-text">Calculating TTM Squeeze...</div>
            `;

            // Reset metrics
            document.getElementById('rsiValue').textContent = 'Loading...';
            document.getElementById('emaValue').textContent = 'Loading...';
            document.getElementById('volumeValue').textContent = 'Loading...';
            document.getElementById('changeValue').textContent = 'Loading...';
            document.getElementById('ttmValue').textContent = 'Loading...';
        }

        // Enhanced Twelve Data Symbol Search Function with debugging
        async function searchSymbolsFromTwelveData(query) {
            console.log(`🔍 Searching for: "${query}"`);

            try {
                // First try Twelve Data symbol search endpoint
                const url = `${TWELVE_DATA_CONFIG.baseUrl}/symbol_search?symbol=${encodeURIComponent(query)}&apikey=${TWELVE_DATA_CONFIG.key}`;
                console.log(`📡 API Call: ${url}`);

                const response = await fetch(url);
                const data = await response.json();

                console.log(`📊 API Response:`, data);

                if (data.status === 'error') {
                    console.warn(`⚠️ API Error: ${data.message}`);
                    throw new Error(data.message);
                }

                // Transform Twelve Data response to our format
                if (data.data && data.data.length > 0) {
                    const results = data.data.slice(0, 10).map(item => ({
                        symbol: item.symbol,
                        name: item.instrument_name || item.symbol,
                        exchange: item.exchange || 'US',
                        type: item.instrument_type || 'Common Stock'
                    }));
                    console.log(`✅ Found ${results.length} symbols from API`);
                    return results;
                } else {
                    console.log(`⚠️ No data returned from API, using fallback`);
                    throw new Error('No data returned');
                }

            } catch (error) {
                console.warn('🚨 Twelve Data symbol search failed:', error);
                console.log('🔄 Using fallback symbols...');

                // Return filtered fallback symbols
                const fallbackSymbols = getFallbackSymbols().filter(s => {
                    const symbolMatch = s.symbol.toUpperCase().includes(query.toUpperCase());
                    const nameMatch = s.name.toUpperCase().includes(query.toUpperCase());
                    return symbolMatch || nameMatch;
                }).slice(0, 10);

                console.log(`✅ Found ${fallbackSymbols.length} fallback symbols`);
                return fallbackSymbols;
            }
        }

        // Fallback symbols for when API fails
        function getFallbackSymbols() {
            return [
                { symbol: 'AAPL', name: 'Apple Inc.', exchange: 'NASDAQ', type: 'Common Stock' },
                { symbol: 'MSFT', name: 'Microsoft Corp.', exchange: 'NASDAQ', type: 'Common Stock' },
                { symbol: 'GOOGL', name: 'Alphabet Inc.', exchange: 'NASDAQ', type: 'Common Stock' },
                { symbol: 'AMZN', name: 'Amazon.com Inc.', exchange: 'NASDAQ', type: 'Common Stock' },
                { symbol: 'TSLA', name: 'Tesla Inc.', exchange: 'NASDAQ', type: 'Common Stock' },
                { symbol: 'NVDA', name: 'NVIDIA Corp.', exchange: 'NASDAQ', type: 'Common Stock' },
                { symbol: 'META', name: 'Meta Platforms Inc.', exchange: 'NASDAQ', type: 'Common Stock' },
                { symbol: 'SPY', name: 'SPDR S&P 500 ETF', exchange: 'NYSE', type: 'ETF' },
                { symbol: 'QQQ', name: 'Invesco QQQ Trust', exchange: 'NASDAQ', type: 'ETF' },
                { symbol: 'BTC/USD', name: 'Bitcoin', exchange: 'CRYPTO', type: 'Cryptocurrency' }
            ];
        }

        function initializeEmptyStates() {
            // Initialize empty state messages
            document.getElementById('currentPrice').textContent = 'Select a symbol to view price';
            document.getElementById('priceChange').textContent = '';
            document.getElementById('chartTitle').textContent = 'Select a Symbol to View Chart';
            
            // Reset all metrics to placeholder state
            document.getElementById('rsiValue').textContent = '--';
            document.getElementById('emaValue').textContent = '--';
            document.getElementById('volumeValue').textContent = '--';
            document.getElementById('changeValue').textContent = '--';
            document.getElementById('ttmValue').textContent = '--';
        }
        
        // Twelve Data API Configuration (FREE - 800 calls/day, 8 calls/minute)
        const TWELVE_DATA_CONFIG = {
            key: '84fa398030b34654a4069a9c26dff629',
            baseUrl: 'https://api.twelvedata.com',
            rateLimits: {
                callsPerDay: 800,
                callsPerMinute: 8,
                currentCalls: 0,
                lastResetTime: Date.now()
            }
        };

        // Backup providers for quotes only
        const BACKUP_PROVIDERS = {
            FINNHUB: {
                key: 'cuegv4pr01qkqnpf6060cuegv4pr01qkqnpf606g',
                baseUrl: 'https://finnhub.io/api/v1'
            },
            YAHOO_FINANCE: {
                baseUrl: 'https://query1.finance.yahoo.com/v8/finance/chart'
            }
        };

        // API Cache to reduce redundant calls
        class APICache {
            constructor(ttlMinutes = 2) {
                this.cache = new Map();
                this.ttl = ttlMinutes * 60 * 1000; // Convert to milliseconds
            }

            set(key, value) {
                this.cache.set(key, {
                    value: value,
                    timestamp: Date.now()
                });
            }

            get(key) {
                const item = this.cache.get(key);
                if (!item) return null;

                if (Date.now() - item.timestamp > this.ttl) {
                    this.cache.delete(key);
                    return null;
                }

                return item.value;
            }

            clear() {
                this.cache.clear();
            }
        }

        // Create cache instances with optimized TTL for API efficiency
        const quoteCache = new APICache(2); // 2 minutes for quotes (reduce API calls)
        const indicatorCache = new APICache(10); // 10 minutes for technical indicators (they change slowly)

        // Rate limiting helper with UI updates
        function checkRateLimit() {
            const now = Date.now();
            const oneMinute = 60 * 1000;

            // Reset counter every minute
            if (now - TWELVE_DATA_CONFIG.rateLimits.lastResetTime > oneMinute) {
                TWELVE_DATA_CONFIG.rateLimits.currentCalls = 0;
                TWELVE_DATA_CONFIG.rateLimits.lastResetTime = now;
            }

            // Update UI display
            updateRateLimitDisplay();

            if (TWELVE_DATA_CONFIG.rateLimits.currentCalls >= TWELVE_DATA_CONFIG.rateLimits.callsPerMinute) {
                throw new Error('Rate limit exceeded. Please wait before making more requests.');
            }
        }

        function updateRateLimitDisplay() {
            const apiCallsElement = document.getElementById('apiCallsCount');
            if (apiCallsElement) {
                const current = TWELVE_DATA_CONFIG.rateLimits.currentCalls;
                const max = TWELVE_DATA_CONFIG.rateLimits.callsPerMinute;
                apiCallsElement.textContent = `${current}/${max}`;

                // Color coding based on usage
                if (current >= max * 0.8) {
                    apiCallsElement.style.color = '#ff4757'; // Red when near limit
                } else if (current >= max * 0.6) {
                    apiCallsElement.style.color = '#ffa502'; // Orange when moderate usage
                } else {
                    apiCallsElement.style.color = '#00d4ff'; // Blue when low usage
                }
            }
        }

        // Optimized Twelve Data API Helper Functions
        async function fetchFromTwelveData(endpoint, params = {}) {
            checkRateLimit();

            const url = new URL(`${TWELVE_DATA_CONFIG.baseUrl}/${endpoint}`);
            url.searchParams.append('apikey', TWELVE_DATA_CONFIG.key);

            Object.keys(params).forEach(key => {
                if (params[key] !== undefined && params[key] !== null) {
                    url.searchParams.append(key, params[key]);
                }
            });

            console.log(`🔄 Twelve Data API Call ${TWELVE_DATA_CONFIG.rateLimits.currentCalls + 1}/8: ${endpoint} for ${params.symbol || 'unknown'}`);

            TWELVE_DATA_CONFIG.rateLimits.currentCalls++;

            const response = await fetch(url.toString());
            if (!response.ok) {
                throw new Error(`Twelve Data API error: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            if (data.status === 'error') {
                throw new Error(`Twelve Data error: ${data.message || 'Unknown error'}`);
            }

            return data;
        }

        // Batch fetch multiple indicators to optimize API usage
        async function fetchAllIndicatorsBatch(symbol) {
            const cleanSymbol = symbol.includes(':') ? symbol.split(':')[1] : symbol;
            const batchCacheKey = `batch_${cleanSymbol}`;

            // Check if we have all indicators cached
            const cachedBatch = indicatorCache.get(batchCacheKey);
            if (cachedBatch) {
                console.log(`📋 Using cached batch indicators for ${cleanSymbol}`);
                return cachedBatch;
            }

            try {
                // Fetch all indicators in parallel but respect rate limits
                const [rsiData, emaData, bbandsData] = await Promise.all([
                    fetchFromTwelveData('rsi', { symbol: cleanSymbol, interval: '1day', time_period: 14 }),
                    fetchFromTwelveData('ema', { symbol: cleanSymbol, interval: '1day', time_period: 50 }),
                    fetchFromTwelveData('bbands', { symbol: cleanSymbol, interval: '1day', time_period: 20 })
                ]);

                const indicators = {
                    rsi: rsiData.values?.[0]?.rsi ? parseFloat(rsiData.values[0].rsi) : null,
                    ema: emaData.values?.[0]?.ema ? parseFloat(emaData.values[0].ema) : null,
                    bbands: bbandsData.values?.[0] ? {
                        upper: parseFloat(bbandsData.values[0].upper_band),
                        middle: parseFloat(bbandsData.values[0].middle_band),
                        lower: parseFloat(bbandsData.values[0].lower_band)
                    } : null
                };

                // Cache the batch result
                indicatorCache.set(batchCacheKey, indicators);
                console.log(`✅ Fetched and cached batch indicators for ${cleanSymbol}`);

                return indicators;
            } catch (error) {
                console.error(`❌ Batch indicators fetch failed for ${cleanSymbol}:`, error);
                throw error;
            }
        }
        
        // Optimized Technical Indicators API Class - Using Twelve Data (FREE)
        class TechnicalIndicatorsAPI {
            // Optimized method that uses batch fetching
            static async getAllIndicators(symbol) {
                try {
                    return await fetchAllIndicatorsBatch(symbol);
                } catch (error) {
                    console.error(`❌ Failed to fetch indicators for ${symbol}:`, error);
                    throw error;
                }
            }

            static async getRSI(symbol, interval = '1day', timePeriod = 14) {
                const cleanSymbol = symbol.includes(':') ? symbol.split(':')[1] : symbol;
                const cacheKey = `rsi_${cleanSymbol}_${interval}_${timePeriod}`;

                // Check cache first
                const cached = indicatorCache.get(cacheKey);
                if (cached) {
                    console.log(`📋 Using cached RSI for ${cleanSymbol}: ${cached}`);
                    return cached;
                }

                try {
                    const data = await fetchFromTwelveData('rsi', {
                        symbol: cleanSymbol,
                        interval: interval,
                        time_period: timePeriod
                    });

                    if (data.values && data.values.length > 0) {
                        const rsiValue = parseFloat(data.values[0].rsi);
                        console.log(`✅ RSI for ${cleanSymbol}: ${rsiValue}`);
                        indicatorCache.set(cacheKey, rsiValue);
                        return rsiValue;
                    } else {
                        throw new Error(`No RSI data returned for ${cleanSymbol}`);
                    }
                } catch (error) {
                    console.error(`❌ RSI API Error for ${cleanSymbol}:`, error);
                    throw error;
                }
            }
            
            static async getEMA(symbol, interval = '1day', timePeriod = 50) {
                const cleanSymbol = symbol.includes(':') ? symbol.split(':')[1] : symbol;
                const cacheKey = `ema_${cleanSymbol}_${interval}_${timePeriod}`;

                // Check cache first
                const cached = indicatorCache.get(cacheKey);
                if (cached) {
                    console.log(`📋 Using cached EMA for ${cleanSymbol}: ${cached}`);
                    return cached;
                }

                try {
                    const data = await fetchFromTwelveData('ema', {
                        symbol: cleanSymbol,
                        interval: interval,
                        time_period: timePeriod
                    });

                    if (data.values && data.values.length > 0) {
                        const emaValue = parseFloat(data.values[0].ema);
                        console.log(`✅ EMA for ${cleanSymbol}: ${emaValue}`);
                        indicatorCache.set(cacheKey, emaValue);
                        return emaValue;
                    } else {
                        throw new Error(`No EMA data returned for ${cleanSymbol}`);
                    }
                } catch (error) {
                    console.error(`❌ EMA API Error for ${cleanSymbol}:`, error);
                    throw error;
                }
            }
            
            static async getBollingerBands(symbol, interval = '1day', timePeriod = 20) {
                const cleanSymbol = symbol.includes(':') ? symbol.split(':')[1] : symbol;
                const cacheKey = `bbands_${cleanSymbol}_${interval}_${timePeriod}`;

                // Check cache first
                const cached = indicatorCache.get(cacheKey);
                if (cached) {
                    console.log(`📋 Using cached Bollinger Bands for ${cleanSymbol}:`, cached);
                    return cached;
                }

                try {
                    const data = await fetchFromTwelveData('bbands', {
                        symbol: cleanSymbol,
                        interval: interval,
                        time_period: timePeriod
                    });

                    if (data.values && data.values.length > 0) {
                        const bands = {
                            upper: parseFloat(data.values[0].upper_band),
                            middle: parseFloat(data.values[0].middle_band),
                            lower: parseFloat(data.values[0].lower_band)
                        };
                        console.log(`✅ Bollinger Bands for ${cleanSymbol}:`, bands);
                        indicatorCache.set(cacheKey, bands);
                        return bands;
                    } else {
                        throw new Error(`No Bollinger Bands data returned for ${cleanSymbol}`);
                    }
                } catch (error) {
                    console.error(`❌ Bollinger Bands API Error for ${cleanSymbol}:`, error);
                    throw error;
                }
            }
            
            static async getQuote(symbol) {
                const cleanSymbol = symbol.includes(':') ? symbol.split(':')[1] : symbol;
                const cacheKey = `quote_${cleanSymbol}`;

                // Check cache first
                const cached = quoteCache.get(cacheKey);
                if (cached) {
                    console.log(`📋 Using cached quote for ${cleanSymbol}: $${cached.price}`);
                    return cached;
                }

                // Try multiple providers in order of reliability
                const providers = [
                    () => this.getQuoteFromTwelveData(cleanSymbol),
                    // () => this.getQuoteFromYahoo(cleanSymbol),
                    () => this.getQuoteFromFinnhub(cleanSymbol)
                ];

                for (const provider of providers) {
                    try {
                        const result = await provider();
                        if (result && result.price && !isNaN(result.price)) {
                            console.log(`✅ Successfully fetched quote for ${cleanSymbol}: $${result.price}`);
                            quoteCache.set(cacheKey, result); // Cache the result
                            return result;
                        }
                    } catch (error) {
                        console.warn(`Provider failed for ${cleanSymbol}:`, error.message);
                        continue;
                    }
                }

                throw new Error(`All quote providers failed for symbol: ${cleanSymbol}`);
            }

            static async getQuoteFromTwelveData(symbol) {
                const data = await fetchFromTwelveData('quote', {
                    symbol: symbol
                });

                if (data.price && data.previous_close) {
                    const currentPrice = parseFloat(data.price);
                    const previousClose = parseFloat(data.previous_close);
                    const change = currentPrice - previousClose;
                    const changePercent = (change / previousClose) * 100;

                    return {
                        price: currentPrice,
                        change: change,
                        changePercent: changePercent,
                        volume: parseInt(data.volume) || 0
                    };
                }
                throw new Error('Twelve Data: Invalid response format');
            }

            static async getQuoteFromYahoo(symbol) {
                const response = await fetch(`${BACKUP_PROVIDERS.YAHOO_FINANCE.baseUrl}/${symbol}?interval=1d&range=1d`);
                const data = await response.json();

                if (data.chart?.result?.[0]?.meta) {
                    const meta = data.chart.result[0].meta;
                    const currentPrice = meta.regularMarketPrice || meta.previousClose;
                    const previousClose = meta.previousClose;
                    const change = currentPrice - previousClose;
                    const changePercent = (change / previousClose) * 100;

                    return {
                        price: currentPrice,
                        change: change,
                        changePercent: changePercent,
                        volume: meta.regularMarketVolume || 0
                    };
                }
                throw new Error('Yahoo Finance: Invalid response format');
            }

            static async getQuoteFromFinnhub(symbol) {
                const response = await fetch(`${BACKUP_PROVIDERS.FINNHUB.baseUrl}/quote?symbol=${symbol}&token=${BACKUP_PROVIDERS.FINNHUB.key}`);
                const data = await response.json();

                if (data.c && data.pc) {
                    const currentPrice = data.c;
                    const previousClose = data.pc;
                    const change = currentPrice - previousClose;
                    const changePercent = (change / previousClose) * 100;

                    return {
                        price: currentPrice,
                        change: change,
                        changePercent: changePercent,
                        volume: 0 // Finnhub doesn't provide volume in this endpoint
                    };
                }
                throw new Error('Finnhub: Invalid response format');
            }
            

        }
              class TradingViewSymbolLoader {
            static async loadSymbols() {
                try {
                    // Load symbols from multiple sources
                    const [stocks, etfs, crypto, forex, commodities] = await Promise.all([
                        this.loadStockSymbols(),
                        this.loadETFSymbols(),
                        this.loadCryptoSymbols(),
                        this.loadForexSymbols(),
                        this.loadCommoditySymbols()
                    ]);

                    return {
                        '📊 Major ETFs': etfs,
                        '🚀 Top Stocks': stocks,
                        '₿ Cryptocurrencies': crypto,
                        '💱 Forex Pairs': forex,
                        '🛢️ Commodities': commodities
                    };
                } catch (error) {
                    console.error('Error loading symbols:', error);
                    return this.getFallbackSymbols();
                }
            }

            static async loadStockSymbols() {
                // Most popular stocks from different sectors
                return [
                    { symbol: 'AAPL', name: 'Apple Inc.', exchange: 'NASDAQ' },
                    { symbol: 'MSFT', name: 'Microsoft Corp.', exchange: 'NASDAQ' },
                    { symbol: 'GOOGL', name: 'Alphabet Inc.', exchange: 'NASDAQ' },
                    { symbol: 'AMZN', name: 'Amazon.com Inc.', exchange: 'NASDAQ' },
                    { symbol: 'NVDA', name: 'NVIDIA Corp.', exchange: 'NASDAQ' },
                    { symbol: 'TSLA', name: 'Tesla Inc.', exchange: 'NASDAQ' },
                    { symbol: 'META', name: 'Meta Platforms', exchange: 'NASDAQ' },
                    { symbol: 'NFLX', name: 'Netflix Inc.', exchange: 'NASDAQ' },
                    { symbol: 'AMD', name: 'Advanced Micro Devices', exchange: 'NASDAQ' },
                    { symbol: 'INTC', name: 'Intel Corp.', exchange: 'NASDAQ' },
                    { symbol: 'JPM', name: 'JPMorgan Chase', exchange: 'NYSE' },
                    { symbol: 'BAC', name: 'Bank of America', exchange: 'NYSE' },
                    { symbol: 'WFC', name: 'Wells Fargo', exchange: 'NYSE' },
                    { symbol: 'GS', name: 'Goldman Sachs', exchange: 'NYSE' },
                    { symbol: 'V', name: 'Visa Inc.', exchange: 'NYSE' },
                    { symbol: 'MA', name: 'Mastercard Inc.', exchange: 'NYSE' },
                    { symbol: 'JNJ', name: 'Johnson & Johnson', exchange: 'NYSE' },
                    { symbol: 'UNH', name: 'UnitedHealth Group', exchange: 'NYSE' },
                    { symbol: 'HD', name: 'Home Depot', exchange: 'NYSE' },
                    { symbol: 'WMT', name: 'Walmart Inc.', exchange: 'NYSE' },
                    { symbol: 'DIS', name: 'Walt Disney Co.', exchange: 'NYSE' },
                    { symbol: 'KO', name: 'Coca-Cola Co.', exchange: 'NYSE' },
                    { symbol: 'PFE', name: 'Pfizer Inc.', exchange: 'NYSE' },
                    { symbol: 'XOM', name: 'Exxon Mobil', exchange: 'NYSE' },
                    { symbol: 'CVX', name: 'Chevron Corp.', exchange: 'NYSE' },
                    { symbol: 'PLTR', name: 'Palantir Technologies', exchange: 'NYSE' },
                    { symbol: 'SNOW', name: 'Snowflake Inc.', exchange: 'NYSE' },
                    { symbol: 'COIN', name: 'Coinbase Global', exchange: 'NASDAQ' },
                    { symbol: 'UBER', name: 'Uber Technologies', exchange: 'NYSE' },
                    { symbol: 'ABNB', name: 'Airbnb Inc.', exchange: 'NASDAQ' }
                ];
            }

            static async loadETFSymbols() {
                return [
                    { symbol: 'SPY', name: 'SPDR S&P 500 ETF', exchange: 'ARCA' },
                    { symbol: 'QQQ', name: 'Invesco QQQ ETF', exchange: 'NASDAQ' },
                    { symbol: 'IWM', name: 'iShares Russell 2000 ETF', exchange: 'ARCA' },
                    { symbol: 'DIA', name: 'SPDR Dow Jones ETF', exchange: 'ARCA' },
                    { symbol: 'VTI', name: 'Vanguard Total Stock Market', exchange: 'ARCA' },
                    { symbol: 'VOO', name: 'Vanguard S&P 500 ETF', exchange: 'ARCA' },
                    { symbol: 'VEA', name: 'Vanguard FTSE Developed Markets', exchange: 'ARCA' },
                    { symbol: 'VWO', name: 'Vanguard FTSE Emerging Markets', exchange: 'ARCA' },
                    { symbol: 'GLD', name: 'SPDR Gold Trust', exchange: 'ARCA' },
                    { symbol: 'SLV', name: 'iShares Silver Trust', exchange: 'ARCA' },
                    { symbol: 'TLT', name: 'iShares 20+ Year Treasury Bond', exchange: 'NASDAQ' },
                    { symbol: 'XLE', name: 'Energy Select Sector SPDR', exchange: 'ARCA' },
                    { symbol: 'XLF', name: 'Financial Select Sector SPDR', exchange: 'ARCA' },
                    { symbol: 'XLK', name: 'Technology Select Sector SPDR', exchange: 'ARCA' },
                    { symbol: 'VIX', name: 'CBOE Volatility Index', exchange: 'CBOE' }
                ];
            }

            static async loadCryptoSymbols() {
                return [
                    { symbol: 'BTCUSD', name: 'Bitcoin / US Dollar', exchange: 'COINBASE' },
                    { symbol: 'ETHUSD', name: 'Ethereum / US Dollar', exchange: 'COINBASE' },
                    { symbol: 'ADAUSD', name: 'Cardano / US Dollar', exchange: 'COINBASE' },
                    { symbol: 'SOLUSD', name: 'Solana / US Dollar', exchange: 'COINBASE' },
                    { symbol: 'DOTUSD', name: 'Polkadot / US Dollar', exchange: 'COINBASE' },
                    { symbol: 'AVAXUSD', name: 'Avalanche / US Dollar', exchange: 'COINBASE' },
                    { symbol: 'MATICUSD', name: 'Polygon / US Dollar', exchange: 'COINBASE' },
                    { symbol: 'LINKUSD', name: 'Chainlink / US Dollar', exchange: 'COINBASE' }
                ];
            }

            static async loadForexSymbols() {
                return [
                    { symbol: 'EURUSD', name: 'Euro / US Dollar', exchange: 'FX_IDC' },
                    { symbol: 'GBPUSD', name: 'British Pound / US Dollar', exchange: 'FX_IDC' },
                    { symbol: 'USDJPY', name: 'US Dollar / Japanese Yen', exchange: 'FX_IDC' },
                    { symbol: 'USDCHF', name: 'US Dollar / Swiss Franc', exchange: 'FX_IDC' },
                    { symbol: 'AUDUSD', name: 'Australian Dollar / US Dollar', exchange: 'FX_IDC' },
                    { symbol: 'USDCAD', name: 'US Dollar / Canadian Dollar', exchange: 'FX_IDC' },
                    { symbol: 'NZDUSD', name: 'New Zealand Dollar / US Dollar', exchange: 'FX_IDC' },
                    { symbol: 'EURGBP', name: 'Euro / British Pound', exchange: 'FX_IDC' },
                    { symbol: 'EURJPY', name: 'Euro / Japanese Yen', exchange: 'FX_IDC' },
                    { symbol: 'GBPJPY', name: 'British Pound / Japanese Yen', exchange: 'FX_IDC' }
                ];
            }

            static async loadCommoditySymbols() {
                return [
                    { symbol: 'GC1!', name: 'Gold Futures', exchange: 'COMEX' },
                    { symbol: 'SI1!', name: 'Silver Futures', exchange: 'COMEX' },
                    { symbol: 'CL1!', name: 'Crude Oil Futures', exchange: 'NYMEX' },
                    { symbol: 'NG1!', name: 'Natural Gas Futures', exchange: 'NYMEX' },
                    { symbol: 'ZC1!', name: 'Corn Futures', exchange: 'CBOT' },
                    { symbol: 'ZS1!', name: 'Soybean Futures', exchange: 'CBOT' },
                    { symbol: 'ZW1!', name: 'Wheat Futures', exchange: 'CBOT' },
                    { symbol: 'HG1!', name: 'Copper Futures', exchange: 'COMEX' },
                    { symbol: 'PL1!', name: 'Platinum Futures', exchange: 'NYMEX' },
                    { symbol: 'PA1!', name: 'Palladium Futures', exchange: 'NYMEX' }
                ];
            }

            static getFallbackSymbols() {
                return {
                    '📊 Major ETFs': [
                        { symbol: 'SPY', name: 'SPDR S&P 500 ETF', exchange: 'ARCA' },
                        { symbol: 'QQQ', name: 'Invesco QQQ ETF', exchange: 'NASDAQ' },
                        { symbol: 'IWM', name: 'iShares Russell 2000 ETF', exchange: 'ARCA' }
                    ],
                    '🚀 Top Stocks': [
                        { symbol: 'AAPL', name: 'Apple Inc.', exchange: 'NASDAQ' },
                        { symbol: 'MSFT', name: 'Microsoft Corp.', exchange: 'NASDAQ' },
                        { symbol: 'GOOGL', name: 'Alphabet Inc.', exchange: 'NASDAQ' }
                    ],
                    '₿ Cryptocurrencies': [
                        { symbol: 'BTCUSD', name: 'Bitcoin / US Dollar', exchange: 'COINBASE' },
                        { symbol: 'ETHUSD', name: 'Ethereum / US Dollar', exchange: 'COINBASE' }
                    ],
                    '💱 Forex Pairs': [
                        { symbol: 'EURUSD', name: 'Euro / US Dollar', exchange: 'FX_IDC' },
                        { symbol: 'GBPUSD', name: 'British Pound / US Dollar', exchange: 'FX_IDC' }
                    ],
                    '🛢️ Commodities': [
                        { symbol: 'GC1!', name: 'Gold Futures', exchange: 'COMEX' },
                        { symbol: 'CL1!', name: 'Crude Oil Futures', exchange: 'NYMEX' }
                    ]
                };
            }

            static formatSymbolForTradingView(symbol, exchange) {
                // Format symbol for TradingView
                const exchangeMap = {
                    'NASDAQ': 'NASDAQ',
                    'NYSE': 'NYSE',
                    'ARCA': 'AMEX',
                    'CBOE': 'CBOE',
                    'COINBASE': 'COINBASE',
                    'FX_IDC': 'FX_IDC',
                    'COMEX': 'COMEX',
                    'NYMEX': 'NYMEX',
                    'CBOT': 'CBOT'
                };
                
                const tvExchange = exchangeMap[exchange] || 'NASDAQ';
                return `${tvExchange}:${symbol}`;
            }
        }

        // Load symbols into dropdown
        async function loadSymbols() {
            const symbolSelect = document.getElementById('symbol');
            const majorSymbols = [
                { symbol: 'SPY', name: 'SPDR S&P 500 ETF', exchange: 'AMEX' },
                { symbol: 'QQQ', name: 'Invesco QQQ ETF', exchange: 'NASDAQ' },
                { symbol: 'AAPL', name: 'Apple Inc.', exchange: 'NASDAQ' },
                { symbol: 'MSFT', name: 'Microsoft Corp.', exchange: 'NASDAQ' },
                { symbol: 'TSLA', name: 'Tesla Inc.', exchange: 'NASDAQ' },
                { symbol: 'NVDA', name: 'NVIDIA Corp.', exchange: 'NASDAQ' },
                { symbol: 'AMZN', name: 'Amazon.com Inc.', exchange: 'NASDAQ' },
                { symbol: 'META', name: 'Meta Platforms', exchange: 'NASDAQ' },
                { symbol: 'GOOGL', name: 'Alphabet Inc.', exchange: 'NASDAQ' },
                { symbol: 'JPM', name: 'JPMorgan Chase', exchange: 'NYSE' },
                { symbol: 'V', name: 'Visa Inc.', exchange: 'NYSE' },
                { symbol: 'XOM', name: 'Exxon Mobil', exchange: 'NYSE' },
                { symbol: 'BRK.B', name: 'Berkshire Hathaway B', exchange: 'NYSE' },
                { symbol: 'UNH', name: 'UnitedHealth Group', exchange: 'NYSE' },
                { symbol: 'HD', name: 'Home Depot', exchange: 'NYSE' },
                { symbol: 'WMT', name: 'Walmart Inc.', exchange: 'NYSE' }
            ];
            symbolSelect.innerHTML = '';
            const optgroup = document.createElement('optgroup');
            optgroup.label = 'Major Symbols';
            for (const stock of majorSymbols) {
                const option = document.createElement('option');
                option.value = `${stock.exchange}:${stock.symbol}`;
                option.textContent = `${stock.symbol} - ${stock.name}`;
                if (stock.symbol === 'SPY') option.selected = true;
                optgroup.appendChild(option);
            }
            symbolSelect.appendChild(optgroup);
        }
        // TTM Squeeze Calculator
         class BeardySqueezeCalculator {
            static calculateBeardySqueeze(priceData, length = 20) {
                const prices = priceData.slice(-length * 2); // Get enough data for calculations
                if (prices.length < length) {
                    return this.generateMockBeardySqueeze();
                }
                
                // Calculate Bollinger Bands
                const bb = this.calculateBollingerBands(prices, length, 2.0);
                
                // Calculate Keltner Channels (multiple levels)
                const kc_high = this.calculateKeltnerChannel(prices, length, 1.0);
                const kc_mid = this.calculateKeltnerChannel(prices, length, 1.5);
                const kc_low = this.calculateKeltnerChannel(prices, length, 2.0);
                
                // Calculate squeeze conditions
                const noSqz = bb.lower < kc_low.lower || bb.upper > kc_low.upper;
                const lowSqz = bb.lower >= kc_low.lower && bb.upper <= kc_low.upper;
                const midSqz = bb.lower >= kc_mid.lower && bb.upper <= kc_mid.upper;
                const highSqz = bb.lower >= kc_high.lower && bb.upper <= kc_high.upper;
                
                // Calculate momentum oscillator
                const momentum = this.calculateMomentum(prices, length);
                
                // Determine squeeze state
                let squeezeState;
                let squeezeColor;
                if (highSqz) {
                    squeezeState = 'HIGH COMPRESSION';
                    squeezeColor = 'orange';
                } else if (midSqz) {
                    squeezeState = 'MID COMPRESSION';
                    squeezeColor = 'red';
                } else if (lowSqz) {
                    squeezeState = 'LOW COMPRESSION';
                    squeezeColor = 'black';
                } else {
                    squeezeState = 'NO SQUEEZE';
                    squeezeColor = 'green';
                }
                
                return {
                    state: squeezeState,
                    color: squeezeColor,
                    momentum: momentum,
                    bb: bb,
                    kc_high: kc_high,
                    kc_mid: kc_mid,
                    kc_low: kc_low,
                    noSqz: noSqz,
                    lowSqz: lowSqz,
                    midSqz: midSqz,
                    highSqz: highSqz
                };
            }
            
            static calculateBollingerBands(prices, length, mult) {
                const closes = prices.map(p => p.close || p);
                const sma = this.calculateSMA(closes, length);
                const stdDev = this.calculateStdDev(closes, length, sma);
                
                return {
                    upper: sma + (mult * stdDev),
                    middle: sma,
                    lower: sma - (mult * stdDev)
                };
            }
            
            static calculateKeltnerChannel(prices, length, mult) {
                const closes = prices.map(p => p.close || p);
                const highs = prices.map(p => p.high || p * 1.001);
                const lows = prices.map(p => p.low || p * 0.999);
                
                const basis = this.calculateSMA(closes, length);
                const tr = this.calculateTrueRange(highs, lows, closes);
                const atr = this.calculateSMA(tr, length);
                
                return {
                    upper: basis + (mult * atr),
                    middle: basis,
                    lower: basis - (mult * atr)
                };
            }
            
            static calculateMomentum(prices, length) {
                const closes = prices.map(p => p.close || p);
                const highs = prices.map(p => p.high || p * 1.001);
                const lows = prices.map(p => p.low || p * 0.999);
                
                if (closes.length < length) return 0;
                
                // Linear regression of (close - average of highest high, lowest low, and SMA)
                const highest = Math.max(...highs.slice(-length));
                const lowest = Math.min(...lows.slice(-length));
                const sma = this.calculateSMA(closes, length);
                const avg = (highest + lowest + sma) / 3;
                
                const values = closes.slice(-length).map(close => close - avg);
                return this.calculateLinearRegression(values)[0] || 0; // Return slope
            }
            
            static calculateSMA(values, length) {
                if (values.length < length) return values[values.length - 1] || 0;
                const slice = values.slice(-length);
                return slice.reduce((sum, val) => sum + val, 0) / length;
            }
            
            static calculateStdDev(values, length, mean) {
                if (values.length < length) return 0;
                const slice = values.slice(-length);
                const variance = slice.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / length;
                return Math.sqrt(variance);
            }
            
            static calculateTrueRange(highs, lows, closes) {
                const tr = [];
                for (let i = 1; i < highs.length; i++) {
                    const high = highs[i];
                    const low = lows[i];
                    const prevClose = closes[i - 1];
                    
                    const tr1 = high - low;
                    const tr2 = Math.abs(high - prevClose);
                    const tr3 = Math.abs(low - prevClose);
                    
                    tr.push(Math.max(tr1, tr2, tr3));
                }
                return tr;
            }
            
            static calculateLinearRegression(values) {
                const n = values.length;
                if (n === 0) return [0, 0];
                
                const xValues = Array.from({length: n}, (_, i) => i);
                const xSum = xValues.reduce((sum, x) => sum + x, 0);
                const ySum = values.reduce((sum, y) => sum + y, 0);
                const xxSum = xValues.reduce((sum, x) => sum + x * x, 0);
                const xySum = xValues.reduce((sum, x, i) => sum + x * values[i], 0);
                
                const slope = (n * xySum - xSum * ySum) / (n * xxSum - xSum * xSum);
                const intercept = (ySum - slope * xSum) / n;
                
                return [slope, intercept];
            }
            
            static generateMockBeardySqueeze() {
                const states = ['NO SQUEEZE', 'LOW COMPRESSION', 'MID COMPRESSION', 'HIGH COMPRESSION'];
                const colors = ['green', 'black', 'red', 'orange'];
                const index = Math.floor(Math.random() * states.length);
                
                return {
                    state: states[index],
                    color: colors[index],
                    momentum: (Math.random() - 0.5) * 4,
                    noSqz: index === 0,
                    lowSqz: index === 1,
                    midSqz: index === 2,
                    highSqz: index === 3
                };
            }
            
            static generateMockPriceHistory() {
                const basePrice = 420;
                const history = [];
                for (let i = 0; i < 50; i++) {
                    const price = basePrice + (Math.random() - 0.5) * 20;
                    history.push({
                        close: price,
                        high: price * 1.002,
                        low: price * 0.998,
                        volume: Math.floor(1000000 + Math.random() * 5000000)
                    });
                }
                return history;
            }
        }
        
        // Accurate TTM Squeeze Calculator (Based on Pine Script Logic)
        class TTMSqueezeCalculator {
            static async calculateTTMSqueeze(symbol, interval = '1day') {
                const cleanSymbol = symbol.includes(':') ? symbol.split(':')[1] : symbol;
                const cacheKey = `ttm_${cleanSymbol}_${interval}`;

                // Check cache first
                const cached = indicatorCache.get(cacheKey);
                if (cached) {
                    console.log(`📋 Using cached TTM Squeeze for ${cleanSymbol}`);
                    return cached;
                }

                try {
                    // Get historical OHLC data from Twelve Data (need more data for accurate calculations)
                    const data = await fetchFromTwelveData('time_series', {
                        symbol: cleanSymbol,
                        interval: interval,
                        outputsize: 100 // Get 100 periods for accurate ATR and momentum calculations
                    });

                    if (data.values && data.values.length >= 50) {
                        // Prepare price data (Twelve Data format) - reverse to chronological order
                        const priceData = data.values.map(item => ({
                            date: item.datetime,
                            high: parseFloat(item.high),
                            low: parseFloat(item.low),
                            close: parseFloat(item.close),
                            open: parseFloat(item.open)
                        })).reverse();

                        // Calculate TTM Squeeze using exact Pine Script logic
                        const squeezeData = this.calculateAccurateTTMSqueeze(priceData);

                        console.log(`✅ TTM Squeeze for ${cleanSymbol}: ${squeezeData.currentState}`);
                        indicatorCache.set(cacheKey, squeezeData);
                        return squeezeData;
                    } else {
                        throw new Error(`Insufficient historical data for TTM Squeeze calculation (need 50+ periods)`);
                    }
                } catch (error) {
                    console.error(`❌ TTM Squeeze calculation error for ${cleanSymbol}:`, error);
                    throw error;
                }
            }

            // Exact Pine Script TTM Squeeze Logic Implementation
            static calculateAccurateTTMSqueeze(priceData) {
                const length = 20;        // Bollinger Band & Keltner Channel length
                const multBB = 2.0;       // Bollinger Band multiplier
                const multKC = 1.5;       // Keltner Channel multiplier
                const momLength = 12;     // Momentum length

                const closes = priceData.map(p => p.close);
                const highs = priceData.map(p => p.high);
                const lows = priceData.map(p => p.low);

                // Calculate for the last 30 periods to show historical data
                const periods = Math.min(30, priceData.length - length - momLength);
                const results = [];

                for (let i = length + momLength; i < priceData.length && results.length < periods; i++) {
                    // === Bollinger Bands ===
                    const basis = this.sma(closes.slice(i - length, i));
                    const dev = multBB * this.stdev(closes.slice(i - length, i));
                    const upperBB = basis + dev;
                    const lowerBB = basis - dev;

                    // === Keltner Channels ===
                    const atr = this.calculateATRAccurate(priceData.slice(i - length, i));
                    const upperKC = basis + multKC * atr;
                    const lowerKC = basis - multKC * atr;

                    // === Squeeze Detection (exact Pine Script logic) ===
                    const squeezeOn = (lowerBB > lowerKC) && (upperBB < upperKC);
                    const squeezeOff = (lowerBB < lowerKC) && (upperBB > upperKC);

                    // === Momentum Calculation ===
                    const momentum = closes[i] - closes[i - momLength];

                    // === Momentum Color Logic ===
                    let histColor;
                    if (momentum > 0) {
                        histColor = (results.length > 0 && momentum > results[results.length - 1].momentum) ? '#4CAF50' : '#8BC34A'; // green : lime
                    } else {
                        histColor = (results.length > 0 && momentum < results[results.length - 1].momentum) ? '#F44336' : '#D32F2F'; // red : maroon
                    }

                    results.push({
                        date: priceData[i].date,
                        momentum: momentum,
                        squeezeOn: squeezeOn,
                        squeezeOff: squeezeOff,
                        histColor: histColor,
                        upperBB: upperBB,
                        lowerBB: lowerBB,
                        upperKC: upperKC,
                        lowerKC: lowerKC,
                        basis: basis
                    });
                }

                // Get current state (last calculation)
                const current = results[results.length - 1];
                let currentState = 'gray';
                if (current.squeezeOn) currentState = 'red';    // Squeeze ON
                else if (current.squeezeOff) currentState = 'green'; // Squeeze OFF

                return {
                    state: currentState === 'red' ? 'blue' : (currentState === 'green' ? 'yellow' : 'gray'), // Convert to our color scheme
                    currentState: currentState,
                    momentum: current.momentum,
                    squeezeOn: current.squeezeOn,
                    squeezeOff: current.squeezeOff,
                    historicalData: results,
                    isSqueezing: current.squeezeOn,
                    color: currentState === 'red' ? '#F44336' : (currentState === 'green' ? '#4CAF50' : '#9E9E9E')
                };
            }
            
            // Pine Script compatible mathematical functions
            static sma(values) {
                return values.reduce((sum, val) => sum + val, 0) / values.length;
            }

            static stdev(values) {
                const mean = this.sma(values);
                const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
                return Math.sqrt(variance);
            }

            static calculateATRAccurate(priceData) {
                if (priceData.length < 2) return 0;

                const trueRanges = [];
                for (let i = 1; i < priceData.length; i++) {
                    const current = priceData[i];
                    const previous = priceData[i - 1];

                    const tr1 = current.high - current.low;
                    const tr2 = Math.abs(current.high - previous.close);
                    const tr3 = Math.abs(current.low - previous.close);

                    trueRanges.push(Math.max(tr1, tr2, tr3));
                }

                return this.sma(trueRanges);
            }

            // Legacy methods for backward compatibility
            static calculateBollingerBands(priceData, period, stdMultiplier) {
                const closes = priceData.slice(-period).map(p => p.close);
                const sma = this.sma(closes);
                const stdDev = this.stdev(closes);

                return {
                    upper: sma + (stdDev * stdMultiplier),
                    middle: sma,
                    lower: sma - (stdDev * stdMultiplier),
                    width: (stdDev * stdMultiplier * 2)
                };
            }
            
            static calculateKeltnerChannels(priceData, period, atrMultiplier) {
                const closes = priceData.slice(-period).map(p => p.close);
                const sma = closes.reduce((sum, close) => sum + close, 0) / period;
                
                // Calculate Average True Range (ATR)
                const atr = this.calculateATR(priceData, period);
                
                return {
                    upper: sma + (atr * atrMultiplier),
                    middle: sma,
                    lower: sma - (atr * atrMultiplier),
                    width: (atr * atrMultiplier * 2)
                };
            }
            
            static calculateATR(priceData, period) {
                const trueRanges = [];
                
                for (let i = 1; i < priceData.length && i <= period; i++) {
                    const current = priceData[priceData.length - i];
                    const previous = priceData[priceData.length - i - 1];
                    
                    const tr1 = current.high - current.low;
                    const tr2 = Math.abs(current.high - previous.close);
                    const tr3 = Math.abs(current.low - previous.close);
                    
                    trueRanges.push(Math.max(tr1, tr2, tr3));
                }
                
                return trueRanges.reduce((sum, tr) => sum + tr, 0) / trueRanges.length;
            }
            
            static calculateFullSqueezeData(priceData, bb, kc) {
                // Calculate momentum oscillator (similar to TTM Squeeze momentum)
                const momentum = this.calculateMomentum(priceData, 20);

                // Determine squeeze state
                const bbInside = (bb.upper <= kc.upper && bb.lower >= kc.lower);
                let state, color;

                if (bbInside) {
                    state = 'squeeze_on';
                    color = '#2196F3'; // Blue - Squeeze ON (low volatility)
                } else {
                    // Determine expansion phase
                    const expansion = bb.width / kc.width;

                    if (expansion > 1.2) {
                        state = 'squeeze_off_high';
                        color = '#F44336'; // Red - Strong expansion (high volatility)
                    } else {
                        state = 'squeeze_off_medium';
                        color = '#FF9800'; // Orange - Moderate expansion (medium volatility)
                    }
                }

                return {
                    state: state === 'squeeze_on' ? 'blue' : (state === 'squeeze_off_high' ? 'red' : 'yellow'),
                    color: color,
                    momentum: momentum,
                    bollingerBands: bb,
                    keltnerChannels: kc,
                    isSqueezing: bbInside,
                    expansion: bb.width / kc.width
                };
            }

            static calculateMomentum(priceData, period) {
                if (priceData.length < period) return 0;

                // Calculate Linear Regression of closing prices
                const closes = priceData.slice(-period).map(p => p.close);
                const n = closes.length;
                const x = Array.from({length: n}, (_, i) => i);

                // Linear regression calculation
                const sumX = x.reduce((a, b) => a + b, 0);
                const sumY = closes.reduce((a, b) => a + b, 0);
                const sumXY = x.reduce((sum, xi, i) => sum + xi * closes[i], 0);
                const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

                const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);

                return slope * 100; // Scale for visualization
            }

            static determineSqueeze(bb, kc) {
                // Legacy method for backward compatibility
                const bbInside = (bb.upper <= kc.upper && bb.lower >= kc.lower);

                if (bbInside) {
                    return 'blue'; // Squeeze is ON - low volatility
                } else {
                    const expansion = bb.width / kc.width;
                    if (expansion > 1.2) {
                        return 'red'; // Strong expansion - high volatility
                    } else {
                        return 'yellow'; // Moderate expansion - medium volatility
                    }
                }
            }
            
        }
        // Initialize TradingView Widget
        function initTradingViewWidget(tvSymbol) {
            const interval = document.getElementById('timeframe').value;
            if (!tvSymbol) {
                const symbol = document.getElementById('symbol').value;
                tvSymbol = symbol;
            }
            
            console.log('🔄 Initializing TradingView Widget...');
            
            // Clear previous widget
            if (widget) {
                widget.remove();
            }
            
            // Map symbol to TradingView format
            const tradingViewSymbol = getTradingViewSymbol(tvSymbol);
            
            widget = new TradingView.widget({
                "width": "100%",
                "height": 500,
                "symbol": tradingViewSymbol,
                "interval": interval,
                "timezone": "America/New_York",
                "theme": "dark",
                "style": "1",
                "locale": "en",
                "toolbar_bg": "#0f0f23",
                "enable_publishing": false,
                "hide_side_toolbar": false,
                "allow_symbol_change": true,
                "container_id": "tradingview_chart",
                "studies": [
                    "MAExp@tv-basicstudies", // 50 EMA
                    "RSI@tv-basicstudies",   // RSI
                    "Volume@tv-basicstudies", // Volume
                    "BB@tv-basicstudies"  ,
                    "SQUEEZE@tv-basicstudies"    // Bollinger Bands
                ],
                "overrides": {
                    "paneProperties.background": "#0f0f23",
                    "paneProperties.backgroundType": "solid",
                    "scalesProperties.textColor": "#cccccc",
                    "scalesProperties.backgroundColor": "#1a1a2e",
                    "mainSeriesProperties.candleStyle.upColor": "#00ff88",
                    "mainSeriesProperties.candleStyle.downColor": "#ff4757",
                    "mainSeriesProperties.candleStyle.borderUpColor": "#00ff88",
                    "mainSeriesProperties.candleStyle.borderDownColor": "#ff4757"
                },
                "studies_overrides": {
                    "volume.volume.color.0": "#ff4757",
                    "volume.volume.color.1": "#00ff88",
                    "MAExp.plot.color": "#ffffff",
                    "RSI.plot.color": "#00d4ff",
                    "BB.upper.color": "#ffffff",
                    "BB.lower.color": "#ffffff",
                    "BB.median.color": "#cccccc"
                }
            });
            
            console.log('✅ TradingView Widget initialized');
            showToast('📊 TradingView chart loaded successfully!', 'success');
            
            // Start fetching technical indicators
            setTimeout(() => {
                fetchTechnicalIndicators();
            }, 2000);
            
            // Listen for symbol changes in the widget
            widget.onChartReady(function() {
                widget.activeChart().onSymbolChanged().subscribe(null, function(obj) {
                    const newSymbol = obj.name; // e.g., 'AAPL'
                    // Try to match with curated list
                    const symbolSelect = document.getElementById('symbol');
                    let matched = false;
                    for (const optgroup of symbolSelect.children) {
                        for (const option of optgroup.children) {
                            if (option.value.replace(/^[A-Z]+:/, '') === newSymbol) {
                                option.selected = true;
                                matched = true;
                                break;
                            }
                        }
                        if (matched) break;
                    }
                    if (!matched) {
                        symbolSelect.selectedIndex = -1;
                    }
                    document.getElementById('chartTitle').textContent = `${obj.full_name} - TradingView Professional Chart`;
                    // Update dashboard logic for new symbol
                    fetchTechnicalIndicatorsForSymbol(obj.full_name || newSymbol);
                });
            });
        }
        
                function getTradingViewSymbol(symbol) {
            const symbolMap = {
                // Major ETFs
                'SPY': 'AMEX:SPY',      // S&P 500
                'QQQ': 'NASDAQ:QQQ',    // NASDAQ 100
                'IWM': 'AMEX:IWM',      // Russell 2000
                'DIA': 'AMEX:DIA',      // Dow Jones
                'VTI': 'AMEX:VTI',      // Total Stock Market
                'VOO': 'AMEX:VOO',      // Vanguard S&P 500
                'VEA': 'AMEX:VEA',      // Developed Markets
                'VWO': 'AMEX:VWO',      // Emerging Markets
                'TLT': 'NASDAQ:TLT',    // 20+ Year Treasury
                'GLD': 'AMEX:GLD',      // Gold
                'SLV': 'AMEX:SLV',      // Silver
                'USO': 'AMEX:USO',      // Oil
                'XLE': 'AMEX:XLE',      // Energy
                'XLF': 'AMEX:XLF',      // Financial
                'XLK': 'AMEX:XLK',      // Technology
                'XLV': 'AMEX:XLV',      // Healthcare
                'XLRE': 'AMEX:XLRE',    // Real Estate
                
                // FAANG + Major Tech
                'AAPL': 'AAPL',  // Apple
                'MSFT': 'NASDAQ:MSFT',  // Microsoft
                'GOOGL': 'NASDAQ:GOOGL', // Google
                'GOOG': 'NASDAQ:GOOG',  // Google Class C
                'AMZN': 'NASDAQ:AMZN',  // Amazon
                'META': 'NASDAQ:META',  // Meta (Facebook)
                'NFLX': 'NASDAQ:NFLX',  // Netflix
                'NVDA': 'NASDAQ:NVDA',  // Nvidia
                'TSLA': 'NASDAQ:TSLA',  // Tesla
                'AMD': 'NASDAQ:AMD',    // AMD
                'INTC': 'NASDAQ:INTC',  // Intel
                'CRM': 'NYSE:CRM',      // Salesforce
                'ORCL': 'NYSE:ORCL',    // Oracle
                'ADBE': 'NASDAQ:ADBE',  // Adobe
                
                // Major Banks
                'JPM': 'NYSE:JPM',      // JPMorgan Chase
                'BAC': 'NYSE:BAC',      // Bank of America
                'WFC': 'NYSE:WFC',      // Wells Fargo
                'C': 'NYSE:C',          // Citigroup
                'GS': 'NYSE:GS',        // Goldman Sachs
                'MS': 'NYSE:MS',        // Morgan Stanley
                
                // Warren Buffett Holdings
                'BRK.A': 'NYSE:BRK.A',  // Berkshire Hathaway A
                'BRK.B': 'NYSE:BRK.B',  // Berkshire Hathaway B
                'KO': 'NYSE:KO',        // Coca-Cola
                'PG': 'NYSE:PG',        // Procter & Gamble
                'JNJ': 'NYSE:JNJ',      // Johnson & Johnson
                'UNH': 'NYSE:UNH',      // UnitedHealth
                'V': 'NYSE:V',          // Visa
                'MA': 'NYSE:MA',        // Mastercard
                
                // Industrial & Consumer
                'DIS': 'NYSE:DIS',      // Disney
                'HD': 'NYSE:HD',        // Home Depot
                'MCD': 'NYSE:MCD',      // McDonald's
                'NKE': 'NYSE:NKE',      // Nike
                'SBUX': 'NASDAQ:SBUX',  // Starbucks
                'WMT': 'NYSE:WMT',      // Walmart
                'TGT': 'NYSE:TGT',      // Target
                'COST': 'NASDAQ:COST',  // Costco
                
                // Energy & Utilities
                'XOM': 'NYSE:XOM',      // Exxon Mobil
                'CVX': 'NYSE:CVX',      // Chevron
                'NEE': 'NYSE:NEE',      // NextEra Energy
                
                // Healthcare & Pharma
                'PFE': 'NYSE:PFE',      // Pfizer
                'ABBV': 'NYSE:ABBV',    // AbbVie
                'TMO': 'NYSE:TMO',      // Thermo Fisher
                'DHR': 'NYSE:DHR',      // Danaher
                
                // Communication
                'T': 'NYSE:T',          // AT&T
                'VZ': 'NYSE:VZ',        // Verizon
                'CMCSA': 'NASDAQ:CMCSA', // Comcast
                
                // Aerospace & Defense
                'BA': 'NYSE:BA',        // Boeing
                'LMT': 'NYSE:LMT',      // Lockheed Martin
                'RTX': 'NYSE:RTX',      // Raytheon
                
                // Emerging Tech
                'PLTR': 'NYSE:PLTR',    // Palantir
                'SNOW': 'NYSE:SNOW',    // Snowflake
                'COIN': 'NASDAQ:COIN',  // Coinbase
                'RBLX': 'NYSE:RBLX',    // Roblox
                'UBER': 'NYSE:UBER',    // Uber
                'LYFT': 'NASDAQ:LYFT',  // Lyft
                'ABNB': 'NASDAQ:ABNB',  // Airbnb
                'SHOP': 'NYSE:SHOP',    // Shopify
                'SQ': 'NYSE:SQ',        // Block (Square)
                'PYPL': 'NASDAQ:PYPL',  // PayPal
                
                // Chinese ADRs
                'BABA': 'NYSE:BABA',    // Alibaba
                'JD': 'NASDAQ:JD',      // JD.com
                'PDD': 'NASDAQ:PDD',    // PDD Holdings
                'BIDU': 'NASDAQ:BIDU',  // Baidu
                'NIO': 'NYSE:NIO',      // Nio
                'XPEV': 'NYSE:XPEV',    // XPeng
                'LI': 'NASDAQ:LI',      // Li Auto
                
                // Crypto-Related
                'MSTR': 'NASDAQ:MSTR',  // MicroStrategy
                'RIOT': 'NASDAQ:RIOT',  // Riot Platforms
                'MARA': 'NASDAQ:MARA',  // Marathon Digital
                
                // Commodities & Materials
                'FCX': 'NYSE:FCX',      // Freeport-McMoRan
                'NEM': 'NYSE:NEM',      // Newmont
                'AA': 'NYSE:AA',        // Alcoa
                
                // REITs
                'AMT': 'NYSE:AMT',      // American Tower
                'PLD': 'NYSE:PLD',      // Prologis
                'CCI': 'NYSE:CCI',      // Crown Castle
                
                // International Indices
                'EWJ': 'AMEX:EWJ',      // Japan ETF
                'EWZ': 'AMEX:EWZ',      // Brazil ETF
                'FXI': 'AMEX:FXI',      // China ETF
                'EWG': 'AMEX:EWG',      // Germany ETF
                'EWU': 'AMEX:EWU',      // UK ETF
                
                // Volatility & Inverse ETFs
                'VIX': 'CBOE:VIX',      // VIX Index
                'UVXY': 'NASDAQ:UVXY',  // Ultra VIX Short-Term
                'SQQQ': 'NASDAQ:SQQQ',  // ProShares UltraPro Short QQQ
                'SPXS': 'AMEX:SPXS',    // Direxion Daily S&P 500 Bear
                
                // Forex Major Pairs
                'EURUSD': 'FX_IDC:EURUSD',
                'GBPUSD': 'FX_IDC:GBPUSD',
                'USDJPY': 'FX_IDC:USDJPY',
                'USDCHF': 'FX_IDC:USDCHF',
                'AUDUSD': 'FX_IDC:AUDUSD',
                'USDCAD': 'FX_IDC:USDCAD',
                'NZDUSD': 'FX_IDC:NZDUSD',
                
                // Crypto
                'BTCUSD': 'COINBASE:BTCUSD',
                'ETHUSD': 'COINBASE:ETHUSD',
                'ADAUSD': 'COINBASE:ADAUSD',
                'DOTUSD': 'COINBASE:DOTUSD',
                'SOLUSD': 'COINBASE:SOLUSD',
                
                // Commodities Futures
                'CL1!': 'NYMEX:CL1!',   // Crude Oil
                'GC1!': 'COMEX:GC1!',   // Gold Futures
                'SI1!': 'COMEX:SI1!',   // Silver Futures
                'NG1!': 'NYMEX:NG1!',   // Natural Gas
                'ZC1!': 'CBOT:ZC1!',    // Corn
                'ZS1!': 'CBOT:ZS1!',    // Soybeans
                'ZW1!': 'CBOT:ZW1!'     // Wheat
            };
            return symbolMap[symbol] || `${symbol}`;
        }

        // Optimized fetch using batch API calls to minimize rate limit usage
        async function fetchTechnicalIndicators() {

            // Accept symbol as argument for robust data flow
            let symbol = arguments[0] || currentSymbol;
            if (!symbol) {
                console.log('No symbol selected, skipping technical indicators fetch');
                return;
            }
            // Clean the symbol if it has an exchange prefix
            const cleanSymbol = symbol.includes(':') ? symbol.split(':')[1] : symbol;
            const interval = getTwelveDataInterval();

            console.log(`🔍 Fetching data for ${symbol}...`);
            showLoadingStates();

            try {
                // Fetch all required data in parallel using optimized batch approach
                const results = await Promise.allSettled([
                    TechnicalIndicatorsAPI.getQuote(cleanSymbol),
                    TechnicalIndicatorsAPI.getAllIndicators(cleanSymbol),
                    TTMSqueezeCalculator.calculateTTMSqueeze(cleanSymbol, interval)
                ]);

                // Check if we have at least the quote data
                if (results[0].status === 'rejected') {
                    throw new Error(`Failed to fetch quote data: ${results[0].reason.message}`);
                }

                const quote = results[0].value;
                let rsi = null, ema = null, bollingerBands = null;

                // Extract indicators from batch result
                if (results[1].status === 'fulfilled' && results[1].value) {
                    const indicators = results[1].value;
                    rsi = indicators.rsi;
                    ema = indicators.ema;
                    bollingerBands = indicators.bbands;
                }

                const ttmSqueeze = results[2].status === 'fulfilled' ? results[2].value : null;

                // Log any failed indicators
                if (results[1].status === 'rejected') {
                    console.warn(`⚠️ Batch indicators failed:`, results[1].reason.message);
                }
                if (results[2].status === 'rejected') {
                    console.warn(`⚠️ TTM Squeeze failed:`, results[2].reason.message);
                }

                // Update the dashboard with available data
                updateDashboard(quote, rsi, ema, bollingerBands, ttmSqueeze);

                // Show appropriate success/warning message
                const failedCount = results.filter(r => r.status === 'rejected').length;
                if (failedCount === 0) {
                    console.log('✅ All technical indicators updated successfully (optimized)');
                    showToast('📊 All indicators loaded successfully!', 'success');
                } else if (failedCount < results.length) {
                    console.log(`⚠️ Some indicators loaded successfully (optimized)`);
                    showToast(`⚠️ Some indicators failed to load`, 'warning');
                }

            } catch (error) {
                console.error('❌ Critical error fetching technical indicators:', error);
                showToast(`❌ Failed to load data: ${error.message}`, 'error');

                // Show error state in dashboard
                updateDashboardError(symbol, error.message);
            }
        }
        
        function getAlphaVantageInterval() {
            const interval = document.getElementById('timeframe').value;
           const intervalMap = {
                '2': '2min',
                '60': '60min',
                '240': '4hour',
                'D': 'daily'
            };
            return intervalMap[interval] || 'daily';
        }

        // Twelve Data interval mapping (optimized for their API)
        function getTwelveDataInterval() {
            const interval = document.getElementById('timeframe').value;
            const intervalMap = {
                '1': '1min',
                '2': '2min',
                '5': '5min',
                '15': '15min',
                '30': '30min',
                '45': '45min',
                '60': '1h',
                '120': '2h',
                '240': '4h',
                '480': '8h',
                'D': '1day',
                'W': '1week',
                'M': '1month'
            };
            return intervalMap[interval] || '1day'; // Default to daily for best data availability
        }
        
        function updateDashboard(quote, rsi, ema, bollingerBands, ttmSqueeze) {
            if (!currentSymbol) {
                console.error('No symbol selected in updateDashboard');
                return;
            }
            const symbol = currentSymbol.includes(':') ? currentSymbol.split(':')[1] : currentSymbol;

            // Clear loading states
            document.querySelectorAll('.loading-text').forEach(el => {
                el.style.display = 'none';
            });

            // Update price display (quote is required)
            if (quote && quote.price) {
                document.getElementById('currentPrice').textContent = `${symbol}: $${quote.price.toFixed(2)}`;

                // Update price change
                const changeElement = document.getElementById('priceChange');
                const changeText = `${quote.change >= 0 ? '+' : ''}${quote.change.toFixed(2)} (${quote.changePercent >= 0 ? '+' : ''}${quote.changePercent.toFixed(2)}%)`;
                changeElement.textContent = changeText;
                changeElement.style.color = quote.change >= 0 ? '#00ff88' : '#ff4757';

                // Update volume
                document.getElementById('volumeValue').textContent = quote.volume ? (quote.volume / 1000000).toFixed(1) + 'M' : 'N/A';
                document.getElementById('changeValue').textContent = `${quote.changePercent >= 0 ? '+' : ''}${quote.changePercent.toFixed(2)}%`;
            }

            // Update metrics cards with null checks
            document.getElementById('rsiValue').textContent = rsi ? rsi.toFixed(1) : 'Loading...';
            document.getElementById('emaValue').textContent = ema ? `$${ema.toFixed(2)}` : 'Loading...';

            // Update trading signals if we have all required data
            if (quote && quote.price && rsi && ema) {
                // Analyze signals will update the buy/sell signal displays
                analyzeSignals(quote, rsi, ema, bollingerBands, ttmSqueeze);
            } else {
                // Show loading state for signals
                document.getElementById('buySignal').innerHTML = `
                    <div class="loading-text">Calculating buy conditions...</div>
                `;
                document.getElementById('sellSignal').innerHTML = `
                    <div class="loading-text">Calculating sell conditions...</div>
                `;
            }

            // Update TTM Squeeze
            const ttmElement = document.getElementById('ttmValue');
            if (ttmSqueeze) {
                let ttmInfo;
                if (typeof ttmSqueeze === 'object') {
                    // New enhanced TTM Squeeze data
                    const stateColors = {
                        'blue': { text: 'SQUEEZE', color: '#2196F3' },
                        'yellow': { text: 'SETUP', color: '#FF9800' },
                        'red': { text: 'FIRE', color: '#F44336' }
                    };
                    ttmInfo = stateColors[ttmSqueeze.state] || { text: 'UNKNOWN', color: '#888' };

                    // Draw TTM Squeeze chart
                    drawTTMSqueezeChart(ttmSqueeze);
                } else {
                    // Legacy string format
                    const ttmColors = {
                        'blue': { text: 'SQUEEZE', color: '#4285f4' },
                        'yellow': { text: 'SETUP', color: '#ffb700' },
                        'red': { text: 'FIRE', color: '#ff4757' }
                    };
                    ttmInfo = ttmColors[ttmSqueeze] || { text: 'UNKNOWN', color: '#888' };
                }

                ttmElement.textContent = ttmInfo.text;
                ttmElement.style.color = ttmInfo.color;
            } else {
                ttmElement.textContent = 'Loading...';
                ttmElement.style.color = '#888';
            }

            // Analyze and update signals only if we have the required data
            if (quote && rsi !== null && ema !== null) {
                analyzeSignals(quote, rsi, ema, bollingerBands, ttmSqueeze);
            } else {
                // Show loading state for signals
                updateSignalDisplay(false, false, 0, 0);
            }

        }

        // Accurate TTM Squeeze Chart Drawing Functions (Pine Script Style)
        function drawTTMSqueezeChart(ttmData) {
            const canvas = document.getElementById('ttmSqueezeChart');
            if (!canvas) return;

            // Set canvas size for high DPI displays
            const rect = canvas.getBoundingClientRect();
            const dpr = window.devicePixelRatio || 1;
            canvas.width = rect.width * dpr;
            canvas.height = rect.height * dpr;

            const ctx = canvas.getContext('2d');
            ctx.scale(dpr, dpr);

            const width = rect.width;
            const height = rect.height;

            // Clear canvas
            ctx.clearRect(0, 0, width, height);

            // Set up chart parameters
            const padding = 40;
            const chartWidth = width - 2 * padding;
            const chartHeight = height - 2 * padding;

            // Check if we have historical data for proper chart
            if (ttmData.historicalData && ttmData.historicalData.length > 0) {
                drawAccurateTTMChart(ctx, ttmData, padding, chartWidth, chartHeight);
            } else {
                // Fallback to simple display
                drawSimpleTTMDisplay(ctx, ttmData, padding, chartWidth, chartHeight);
            }
        }

        function drawAccurateTTMChart(ctx, ttmData, padding, chartWidth, chartHeight) {
            const data = ttmData.historicalData;
            const centerY = padding + chartHeight / 2;

            // Find momentum range for scaling
            const momentums = data.map(d => d.momentum);
            const maxMomentum = Math.max(...momentums.map(Math.abs));
            const scale = maxMomentum > 0 ? (chartHeight * 0.4) / maxMomentum : 1;

            // Draw zero line
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(padding, centerY);
            ctx.lineTo(padding + chartWidth, centerY);
            ctx.stroke();

            // Calculate bar width
            const barWidth = Math.max(2, chartWidth / data.length * 0.8);
            const barSpacing = chartWidth / data.length;

            // Draw momentum histogram bars (Pine Script style)
            data.forEach((point, index) => {
                const x = padding + index * barSpacing + barSpacing / 2 - barWidth / 2;
                const momentum = point.momentum;
                const barHeight = Math.abs(momentum) * scale;

                // Set color based on Pine Script logic
                ctx.fillStyle = point.histColor;

                if (momentum > 0) {
                    // Positive momentum - bar goes up from center
                    ctx.fillRect(x, centerY - barHeight, barWidth, barHeight);
                } else {
                    // Negative momentum - bar goes down from center
                    ctx.fillRect(x, centerY, barWidth, barHeight);
                }

                // Draw squeeze indicators at bottom
                const indicatorY = padding + chartHeight - 15;
                const indicatorSize = 4;

                if (point.squeezeOn) {
                    // Red circle for squeeze ON
                    ctx.fillStyle = '#F44336';
                    ctx.beginPath();
                    ctx.arc(x + barWidth/2, indicatorY, indicatorSize, 0, 2 * Math.PI);
                    ctx.fill();
                } else if (point.squeezeOff) {
                    // Green circle for squeeze OFF
                    ctx.fillStyle = '#4CAF50';
                    ctx.beginPath();
                    ctx.arc(x + barWidth/2, indicatorY, indicatorSize, 0, 2 * Math.PI);
                    ctx.fill();
                } else {
                    // Gray circle for no clear state
                    ctx.fillStyle = '#9E9E9E';
                    ctx.beginPath();
                    ctx.arc(x + barWidth/2, indicatorY, indicatorSize * 0.7, 0, 2 * Math.PI);
                    ctx.fill();
                }
            });

            // Draw current state indicator
            drawCurrentStateIndicator(ctx, ttmData, padding, chartWidth, chartHeight);

            // Draw labels and legend
            drawTTMLabels(ctx, ttmData, padding, chartWidth, chartHeight, maxMomentum);
        }

        function drawSimpleTTMDisplay(ctx, ttmData, padding, chartWidth, chartHeight) {
            const centerY = padding + chartHeight / 2;

            // Draw simple momentum bar
            const momentum = ttmData.momentum || 0;
            const barWidth = 40;
            const maxMomentum = 5;
            const barHeight = Math.min(Math.abs(momentum) * 20, chartHeight / 3);

            ctx.fillStyle = momentum > 0 ? '#4CAF50' : '#F44336';
            const barX = padding + chartWidth / 2 - barWidth / 2;
            const barY = momentum > 0 ? centerY - barHeight : centerY;

            ctx.fillRect(barX, barY, barWidth, barHeight);

            // Draw state indicator
            ctx.fillStyle = ttmData.color || '#9E9E9E';
            ctx.beginPath();
            ctx.arc(padding + 30, centerY, 15, 0, 2 * Math.PI);
            ctx.fill();

            // Labels
            ctx.fillStyle = '#FFFFFF';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`Momentum: ${momentum.toFixed(2)}`, padding + chartWidth / 2, centerY + chartHeight / 2 + 20);
        }

        function drawCurrentStateIndicator(ctx, ttmData, padding, chartWidth, chartHeight) {
            // Draw current state box in top right
            const boxWidth = 120;
            const boxHeight = 30;
            const boxX = padding + chartWidth - boxWidth - 10;
            const boxY = padding + 10;

            // Background
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(boxX, boxY, boxWidth, boxHeight);

            // Border
            ctx.strokeStyle = ttmData.color || '#9E9E9E';
            ctx.lineWidth = 2;
            ctx.strokeRect(boxX, boxY, boxWidth, boxHeight);

            // Text
            ctx.fillStyle = '#FFFFFF';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            const stateText = ttmData.squeezeOn ? 'SQUEEZE ON' : (ttmData.squeezeOff ? 'SQUEEZE OFF' : 'NO SIGNAL');
            ctx.fillText(stateText, boxX + boxWidth / 2, boxY + boxHeight / 2 + 4);
        }

        function drawTTMLabels(ctx, ttmData, padding, chartWidth, chartHeight, maxMomentum) {
            ctx.fillStyle = '#FFFFFF';
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';

            // Title
            ctx.fillText('TTM Squeeze Momentum Histogram', padding, padding - 15);

            // Y-axis labels
            ctx.font = '10px Arial';
            ctx.textAlign = 'right';
            const centerY = padding + chartHeight / 2;

            // Positive momentum label
            ctx.fillText(`+${maxMomentum.toFixed(1)}`, padding - 5, padding + 15);
            // Zero line label
            ctx.fillText('0', padding - 5, centerY + 3);
            // Negative momentum label
            ctx.fillText(`-${maxMomentum.toFixed(1)}`, padding - 5, padding + chartHeight - 5);

            // Legend at bottom
            ctx.textAlign = 'left';
            ctx.font = '10px Arial';
            const legendY = padding + chartHeight + 25;

            ctx.fillStyle = '#F44336';
            ctx.fillText('● Squeeze ON', padding, legendY);

            ctx.fillStyle = '#4CAF50';
            ctx.fillText('● Squeeze OFF', padding + 100, legendY);

            ctx.fillStyle = '#9E9E9E';
            ctx.fillText('● No Signal', padding + 200, legendY);
        }

        function updateDashboardError(symbol, errorMessage) {
            // Show error state in dashboard
            document.getElementById('currentPrice').textContent = `${symbol}: Error Loading`;
            document.getElementById('priceChange').textContent = errorMessage;
            document.getElementById('priceChange').style.color = '#ff4757';

            // Show error in metrics
            document.getElementById('rsiValue').textContent = 'Error';
            document.getElementById('emaValue').textContent = 'Error';
            document.getElementById('volumeValue').textContent = 'Error';
            document.getElementById('changeValue').textContent = 'Error';
            document.getElementById('ttmValue').textContent = 'Error';
            document.getElementById('ttmValue').style.color = '#ff4757';

            // Clear signals
            updateSignalDisplay(false, false, 0, 0);

            // Update last update time
            document.getElementById('lastUpdate').textContent = `Last updated: ${new Date().toLocaleTimeString()}`;

            // Store current data
            currentData = {
                quote,
                rsi,
                ema,
                bollingerBands,
                ttmSqueeze,
                symbol
            };
        }
        
        function analyzeSignals(quote, rsi, ema, bollingerBands, ttmSqueeze) {
            // Update signal boxes with loading state if data is missing
            if (!quote || !quote.price || rsi === null || ema === null) {
                document.getElementById('buySignal').innerHTML = `
                    <div class="signal-title" style="color: #00ff88;">🟢 BUY SIGNAL</div>
                    <div class="loading-spinner"></div>
                    <div class="loading-text">Calculating buy conditions...</div>
                `;
                document.getElementById('sellSignal').innerHTML = `
                    <div class="signal-title" style="color: #ff4757;">🔴 SELL SIGNAL</div>
                    <div class="loading-spinner"></div>
                    <div class="loading-text">Calculating sell conditions...</div>
                `;
                return;
            }

            const currentPrice = quote.price;

            // Extract TTM Squeeze state (handle both old and new formats)
            let ttmState = 'gray'; // Default state
            if (ttmSqueeze) {
                if (typeof ttmSqueeze === 'object' && ttmSqueeze.state) {
                    ttmState = ttmSqueeze.state;
                } else if (typeof ttmSqueeze === 'string') {
                    ttmState = ttmSqueeze;
                }
            } else {
                ttmState = ttmSqueeze; // Legacy string format
            }

            // Buy Conditions
            const buyCondition1 = currentPrice > (ema * 1.01); // Price > EMA + 1%
            const buyCondition2 = ttmState === 'yellow' || ttmState === 'red'; // TTM Setup or Fire
            const buyCondition3 = rsi > 50; // RSI bullish
            const buyCondition4 = bollingerBands && currentPrice > bollingerBands.upper; // Price above BB upper

            // Update buy signal display
            const buySignalElement = document.getElementById('buySignal');
            const shortBuyConditions = [buyCondition1, buyCondition2, buyCondition3, buyCondition4];
            const shortBuyStrength = shortBuyConditions.filter(Boolean).length;
            
            buySignalElement.innerHTML = `
                <div class="signal-strength" style="color: ${shortBuyStrength >= 3 ? '#00ff88' : '#666'}">
                    ${shortBuyStrength}/4 conditions met
                </div>
                <ul class="conditions-list">
                    <li style="color: ${buyCondition1 ? '#00ff88' : '#666'}">Price above EMA trend</li>
                    <li style="color: ${buyCondition2 ? '#00ff88' : '#666'}">TTM squeeze released</li>
                    <li style="color: ${buyCondition3 ? '#00ff88' : '#666'}">RSI bullish trend</li>
                    <li style="color: ${buyCondition4 ? '#00ff88' : '#666'}">Price above upper BB</li>
                </ul>
            `;

            // Sell Conditions
            const sellCondition1 = currentPrice < (ema * 0.99); // Price < EMA - 1%
            const sellCondition2 = ttmState === 'blue'; // TTM Squeeze
            const sellCondition3 = rsi < 50; // RSI bearish
            const sellCondition4 = bollingerBands && currentPrice < bollingerBands.lower; // Price below BB lower
            
            // Update sell signal display
            const sellSignalElement = document.getElementById('sellSignal');
            const shortSellConditions = [sellCondition1, sellCondition2];
            const shortSellStrength = shortSellConditions.filter(Boolean).length;
            
            sellSignalElement.innerHTML = `
                <div class="signal-strength" style="color: ${shortSellStrength >= 2 ? '#ff4757' : '#666'}">
                    ${shortSellStrength}/2 conditions met
                </div>
                <ul class="conditions-list">
                    <li style="color: ${sellCondition1 ? '#ff4757' : '#666'}">Price below EMA trend</li>
                    <li style="color: ${sellCondition2 ? '#ff4757' : '#666'}">TTM squeeze active</li>
                </ul>
            `;
            
            // Update condition indicators
            updateConditionIndicator('buyCondition1', buyCondition1);
            updateConditionIndicator('buyCondition2', buyCondition2);
            updateConditionIndicator('buyCondition3', buyCondition3);
            updateConditionIndicator('buyCondition4', buyCondition4);
            
            updateConditionIndicator('sellCondition1', sellCondition1);
            updateConditionIndicator('sellCondition2', sellCondition2);
            updateConditionIndicator('sellCondition3', sellCondition3);
            updateConditionIndicator('sellCondition4', sellCondition4);
            
            // Calculate final signal strengths
            const allBuyConditions = [buyCondition1, buyCondition2, buyCondition3, buyCondition4];
            const allSellConditions = [sellCondition1, sellCondition2, sellCondition3, sellCondition4];
            
            const finalBuyStrength = (allBuyConditions.filter(c => c).length / allBuyConditions.length) * 100;
            const finalSellStrength = (allSellConditions.filter(c => c).length / allSellConditions.length) * 100;
            
            // Check for full signals
            const buySignalActive = allBuyConditions.every(c => c);
            const sellSignalActive = allSellConditions.every(c => c);
            
            // Update signal display
            updateSignalDisplay(buySignalActive, sellSignalActive, finalBuyStrength, finalSellStrength);
            
            // Check for new signals
            checkForNewSignals(buySignalActive, sellSignalActive, currentData.symbol, currentPrice);
        }
        
        function updateConditionIndicator(elementId, met) {
            const indicator = document.getElementById(elementId);
            if (indicator) {
                indicator.className = `status-indicator ${met ? 'status-met' : 'status-not-met'}`;
            }
        }
        
           function updateSignalDisplay(buyActive, sellActive, buyStrength, sellStrength) {
            const currentSignalElement = document.getElementById('currentSignal');
            const currentSignalBox = document.getElementById('currentSignalBox');
            const buySignalElement = document.getElementById('buySignal');
            const sellSignalElement = document.getElementById('sellSignal');
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');
            
            // Reset classes
            buySignalElement.className = 'signal-box';
            sellSignalElement.className = 'signal-box';
            currentSignalBox.className = 'current-signal-box';
            
            if (buyActive) {
                currentSignalElement.textContent = '🟢 STRONG BUY';
                currentSignalElement.style.color = '#00ff88';
                buySignalElement.classList.add('buy');
                currentSignalBox.classList.add('buy-active');
                strengthFill.style.background = 'linear-gradient(90deg, #00ff88, #00d4ff)';
                strengthFill.style.width = '100%';
                strengthText.textContent = '100% - STRONG BUY';
                strengthText.style.color = '#00ff88';
            } else if (sellActive) {
                currentSignalElement.textContent = '🔴 STRONG SELL';
                currentSignalElement.style.color = '#ff4757';
                sellSignalElement.classList.add('sell');
                currentSignalBox.classList.add('sell-active');
                strengthFill.style.background = 'linear-gradient(90deg, #ff4757, #ff6b7d)';
                strengthFill.style.width = '100%';
                strengthText.textContent = '100% - STRONG SELL';
                strengthText.style.color = '#ff4757';
            } else {
                // For anything in between, show HOLD
                currentSignalElement.textContent = '🟡 HOLD';
                currentSignalElement.style.color = '#ffb700';
                
                const maxStrength = Math.max(buyStrength, sellStrength);
                strengthFill.style.background = 'linear-gradient(90deg, #ffb700, #ffd700)';
                strengthFill.style.width = maxStrength + '%';
                
                if (maxStrength >= 75) {
                    strengthText.textContent = maxStrength.toFixed(0) + '% - STRONG HOLD';
                } else if (maxStrength >= 50) {
                    strengthText.textContent = maxStrength.toFixed(0) + '% - MODERATE HOLD';
                } else {
                    strengthText.textContent = maxStrength.toFixed(0) + '% - WEAK HOLD';
                }
                strengthText.style.color = '#ffb700';
            }
        }
        function checkForNewSignals(buyActive, sellActive, symbol, price) {
            const currentTime = new Date();
            const currentSignal = buyActive ? 'BUY' : (sellActive ? 'SELL' : 'NONE');
            
            // Check if this is a new signal
            if (currentSignal !== 'NONE' && currentSignal !== lastSignal) {
                signalHistory.push({
                    type: currentSignal,
                    symbol: symbol,
                    time: currentTime,
                    price: price
                });
                
                // Update signal count
                document.getElementById('signalCount').textContent = signalHistory.length;
                
                // Add to alert list
                addAlert(currentSignal, symbol, price, currentTime);
                
                // Send notification if enabled
                if (alertsEnabled) {
                    sendAlert(currentSignal, symbol, price);
                }
                
                lastSignal = currentSignal;
            }
        }
        
        function addAlert(type, symbol, price, time) {
            const alertList = document.getElementById('alertList');
            
            // Clear "No alerts" message
            if (alertList.children.length === 1 && alertList.children[0].textContent === 'No alerts yet') {
                alertList.innerHTML = '';
            }
            
            const alertItem = document.createElement('div');
            alertItem.className = 'alert-item';
            alertItem.innerHTML = `
                <div>
                    <span style="color: ${type === 'BUY' ? '#00ff88' : '#ff4757'}; font-weight: bold;">${type} ${symbol}</span>
                    <div style="font-size: 0.9em; color: #ccc;">${price.toFixed(2)}</div>
                </div>
                <div class="alert-time">${time.toLocaleTimeString()}</div>
            `;
            
            // Add to top of list
            alertList.insertBefore(alertItem, alertList.firstChild);
            
            // Keep only last 5 alerts
            while (alertList.children.length > 5) {
                alertList.removeChild(alertList.lastChild);
            }
        }
        
        function sendAlert(type, symbol, price) {
            // Browser notification
            if (Notification.permission === 'granted') {
                const notification = new Notification(`${type} Signal: ${symbol}`, {
                    body: `Price: ${price.toFixed(2)} - All conditions met!`,
                    icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMSA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDMgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjMDBENEZGIi8+Cjwvc3ZnPgo=',
                    tag: `${symbol}-${type}`,
                    requireInteraction: true
                });
                
                // Auto-close after 8 seconds
                setTimeout(() => notification.close(), 8000);
            }
            
            // Console log for debugging
            console.log(`🚨 ${type} SIGNAL FIRED: ${symbol} at ${price.toFixed(2)}`);
        }
        
        function toggleAlerts() {
            const alertBtn = document.getElementById('alertBtn');
            
            if (!alertsEnabled) {
                // Request notification permission
                if ('Notification' in window) {
                    Notification.requestPermission().then(permission => {
                        if (permission === 'granted') {
                            alertsEnabled = true;
                            alertBtn.textContent = '🔕 Disable Alerts';
                            alertBtn.style.background = 'rgba(0, 255, 136, 0.2)';
                            alertBtn.style.borderColor = '#00ff88';
                            showToast('🔔 Alerts enabled! You will receive notifications for new signals.', 'success');
                        } else {
                            showToast('❌ Notification permission denied. Please enable in browser settings.', 'error');
                        }
                    });
                } else {
                    showToast('❌ Notifications not supported in this browser.', 'error');
                }
            } else {
                alertsEnabled = false;
                alertBtn.textContent = '🔔 Enable Alerts';
                alertBtn.style.background = 'rgba(0, 0, 0, 0.3)';
                alertBtn.style.borderColor = 'rgba(255, 255, 255, 0.3)';
                showToast('🔕 Alerts disabled.', 'info');
            }
        }
        
        function showToast(message, type) {
            const toast = document.createElement('div');
            toast.className = 'toast';
            
            const colors = {
                success: 'rgba(0, 255, 136, 0.9)',
                error: 'rgba(255, 71, 87, 0.9)',
                info: 'rgba(0, 212, 255, 0.9)'
            };
            
            toast.style.background = colors[type] || colors.info;
            toast.textContent = message;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 4000);
        }
        
        function refreshData() {
            const refreshBtn = document.getElementById('refreshBtn');
            refreshBtn.textContent = '⏳ Refreshing...';
            refreshBtn.disabled = true;
            
            setTimeout(() => {
                fetchTechnicalIndicators();
                refreshBtn.textContent = '🔄 Refresh Data';
                refreshBtn.disabled = false;
                showToast('📊 Data refreshed successfully!', 'success');
            }, 1000);
        }
        
        function startAutoRefresh() {
            // Refresh every 60 seconds (respecting API limits)
            setInterval(() => {
                if (!document.hidden) {
                    fetchTechnicalIndicators();
                }
            }, 60000);
            
            // More frequent updates during market hours
            setInterval(() => {
                const now = new Date();
                const hour = now.getHours();
                const day = now.getDay();
                
                // US market hours (9:30 AM - 4 PM ET, Mon-Fri)
                if (day >= 1 && day <= 5 && hour >= 9 && hour <= 16 && !document.hidden) {
                    fetchTechnicalIndicators();
                }
            }, 30000);
        }
        
        // Chart indicator controls
        function setupIndicatorControls() {
            const indicatorButtons = document.querySelectorAll('.chart-btn[data-indicator]');
            
            indicatorButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const indicator = this.getAttribute('data-indicator');
                    
                    if (activeIndicators.includes(indicator)) {
                        activeIndicators = activeIndicators.filter(ind => ind !== indicator);
                        this.classList.remove('active');
                    } else {
                        activeIndicators.push(indicator);
                        this.classList.add('active');
                    }
                    
                    showToast(`📊 ${indicator.toUpperCase()} indicator ${activeIndicators.includes(indicator) ? 'added' : 'removed'}`, 'info');
                    
                    // Note: TradingView indicators are added in widget initialization
                    // For dynamic adding/removing, you would need TradingView Charting Library
                });
            });
        }
        
        // Event listeners
        document.getElementById('symbol').addEventListener('change', function() {
            const symbol = this.value;
            document.getElementById('chartTitle').textContent = `${symbol} - TradingView Professional Chart`;
            initTradingViewWidget();
        });
        
        document.getElementById('timeframe').addEventListener('change', function() {
            initTradingViewWidget();
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'r':
                        e.preventDefault();
                        refreshData();
                        break;
                    case 'n':
                        e.preventDefault();
                        toggleAlerts();
                        break;
                }
            }
        });
        
        // Handle page visibility for performance
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                fetchTechnicalIndicators();
            }
        });
        
        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Initializing  Trading Bot Dashboard...');
            
            // Check if TradingView is available
            if (typeof TradingView === 'undefined') {
                console.error('❌ TradingView library not loaded');
                showToast('❌ TradingView library failed to load. Please refresh the page.', 'error');
                return;
            }
            
            // Setup controls
            setupIndicatorControls();
            
            // Initialize TradingView widget
            setTimeout(() => {
                initTradingViewWidget();
            }, 500);
            
            // Start auto refresh
            startAutoRefresh();
            
            // Show welcome message
            setTimeout(() => {
                showToast('📈  Trading Bot Dashboard initialized successfully!', 'success');
            }, 2000);

            // Load default symbol after initialization
            setTimeout(() => {
                const defaultSymbol = 'AAPL'; // Default to Apple
                console.log(`🎯 Loading default symbol: ${defaultSymbol}`);
                selectSymbol(defaultSymbol);
            }, 3000);

            loadSymbols();
        });
        
        // API Rate Limiting Helper
        class APIRateLimiter {
            constructor(maxRequests = 5, timeWindow = 60000) {
                this.maxRequests = maxRequests;
                this.timeWindow = timeWindow;
                this.requests = [];
            }
            
            canMakeRequest() {
                const now = Date.now();
                this.requests = this.requests.filter(time => now - time < this.timeWindow);
                return this.requests.length < this.maxRequests;
            }
            
            recordRequest() {
                this.requests.push(Date.now());
            }
        }
        
        // Create rate limiter instance
        const rateLimiter = new APIRateLimiter(5, 60000); // 5 requests per minute
        
        // Enhanced error handling for API calls
        function handleAPIError(error, fallbackData) {
            console.error('API Error:', error);
            
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                showToast('🌐 Network error - using cached data', 'error');
            } else if (error.message.includes('rate limit')) {
                showToast('⏱️ API rate limit reached - using mock data', 'info');
            } else {
                showToast('⚠️ API unavailable - using simulated data', 'info');
            }
            
            return fallbackData;
        }

        // Removed conflicting event listeners - using the main search system instead

        async function handleSymbolSearch() {
            const input = document.getElementById('symbolSearch').value.trim().toUpperCase();
            if (!input) return;
            
            // Get list of major symbols with their correct exchanges
            const majorSymbols = [
                { symbol: 'SPY', name: 'SPDR S&P 500 ETF', exchange: 'AMEX' },
                { symbol: 'QQQ', name: 'Invesco QQQ ETF', exchange: 'NASDAQ' },
                { symbol: 'AAPL', name: 'Apple Inc.', exchange: 'NASDAQ' },
                { symbol: 'MSFT', name: 'Microsoft Corp.', exchange: 'NASDAQ' },
                { symbol: 'GOOGL', name: 'Alphabet Inc.', exchange: 'NASDAQ' },
                { symbol: 'AMZN', name: 'Amazon.com Inc.', exchange: 'NASDAQ' },
                { symbol: 'META', name: 'Meta Platforms', exchange: 'NASDAQ' },
                { symbol: 'TSLA', name: 'Tesla Inc.', exchange: 'NASDAQ' },
                { symbol: 'JPM', name: 'JPMorgan Chase', exchange: 'NYSE' }
            ];

            // Search for matches
            const matches = majorSymbols.filter(s => 
                s.symbol.includes(input) || 
                s.name.toUpperCase().includes(input)
            );

            if (matches.length > 0) {
                // Use the first match
                const match = matches[0];
                const tvSymbol = `${match.exchange}:${match.symbol}`;
                document.getElementById('chartTitle').textContent = `${tvSymbol} - TradingView Professional Chart`;
                initTradingViewWidget(tvSymbol);
                showToast(`✅ Loaded ${tvSymbol}`, 'success');
                document.getElementById('searchResults').classList.remove('active');
                return;
            }

            // If no matches found in our data, try common exchanges as fallback
            const exchanges = ['NASDAQ', 'NYSE', 'AMEX'];
            for (const ex of exchanges) {
                const tvSymbol = `${ex}:${input}`;
                document.getElementById('chartTitle').textContent = `${tvSymbol} - TradingView Professional Chart`;
                initTradingViewWidget(tvSymbol);
                showToast(`🔍 Trying ${tvSymbol}`, 'info');
                break; // Try first exchange only
            }
        }

        // Helper: fetch indicators for a given symbol (TradingView format)
        function fetchTechnicalIndicatorsForSymbol(tvSymbol) {
            // You may need to parse tvSymbol to get the base symbol for your API calls
            // For example, 'NASDAQ:AAPL' => 'AAPL'
            const baseSymbol = tvSymbol.split(':').pop();
            // Call your existing fetchTechnicalIndicators logic, but pass baseSymbol
            fetchTechnicalIndicators(baseSymbol);
        }

        // Load static US stocks list (assume stocks.js is included with window.US_STOCKS)
        // Example: window.US_STOCKS = [{symbol: 'AAPL', name: 'Apple Inc.', exchange: 'NASDAQ'}, ...];

        // Removed conflicting Awesomplete implementation - using custom search instead

        // Removed conflicting event listeners - using main search system
        // Initialize empty states for all components
        function initializeEmptyStates() {
            // Reset current symbol
            currentSymbol = null;
            
            // Initialize empty chart container
            document.getElementById('tradingview_chart').innerHTML = `
                <div class="empty-state">
                    <div style="text-align: center; padding: 40px; color: #888;">
                        <div style="font-size: 1.2em; margin-bottom: 10px;">📊 Select a symbol to view chart</div>
                        <div style="font-size: 0.9em;">Use the search box above</div>
                    </div>
                </div>
            `;

            // Initialize empty signal boxes
            document.getElementById('buySignal').innerHTML = `
                <div class="signal-title" style="color: #00ff88;">🟢 BUY SIGNAL</div>
                <div class="empty-state" style="text-align: center; padding: 20px; color: #888;">
                    Select a symbol to analyze buy conditions
                </div>
            `;

            document.getElementById('sellSignal').innerHTML = `
                <div class="signal-title" style="color: #ff4757;">🔴 SELL SIGNAL</div>
                <div class="empty-state" style="text-align: center; padding: 20px; color: #888;">
                    Select a symbol to analyze sell conditions
                </div>
            `;

            // Initialize empty metrics
            document.getElementById('rsiValue').textContent = '--';
            document.getElementById('emaValue').textContent = '--';
            document.getElementById('volumeValue').textContent = '--';
            document.getElementById('changeValue').textContent = '--';
            document.getElementById('ttmValue').textContent = '--';
            document.getElementById('currentPrice').textContent = 'Select a symbol...';
            document.getElementById('priceChange').textContent = '';
        }

        // Enhanced symbol search functionality
        function setupSymbolSearch(symbols) {
            const input = document.getElementById('symbolSearch');
            const awesomplete = new Awesomplete(input, {
                list: [],
                minChars: 1,
                maxItems: 8,
                autoFirst: true
            });

            // Transform symbols for display
            const symbolList = symbols.map(s => ({
                label: `${s.symbol} - ${s.name}`,
                value: `${s.exchange}:${s.symbol}`
            }));

            awesomplete.list = symbolList;

            // Show loading state when fetching data
            function showLoadingState() {
                document.getElementById('buySignal').innerHTML = `
                    <div class="signal-title" style="color: #00ff88;">🟢 BUY SIGNAL</div>
                    <div class="loader"></div>
                    <div class="loading-text">Calculating buy conditions...</div>
                `;

                document.getElementById('sellSignal').innerHTML = `
                    <div class="signal-title" style="color: #ff4757;">🔴 SELL SIGNAL</div>
                    <div class="loader"></div>
                    <div class="loading-text">Calculating sell conditions...</div>
                `;

                // Show loading in TTM squeeze
                document.querySelector('.ttm-squeeze-container').innerHTML = `
                    <h4 style="color: #00d4ff; text-align: center; margin-bottom: 15px;">📊 TTM Squeeze Indicator</h4>
                    <div class="loader"></div>
                    <div class="loading-text">Calculating TTM Squeeze...</div>
                `;
            }

            // Handle symbol selection
            input.addEventListener('awesomplete-selectcomplete', function(e) {
                const selectedSymbol = e.text.value;
                
                // Show loading states
                showLoadingState();
                
                // Update symbol select
                const symbolSelect = document.getElementById('symbol');
                symbolSelect.value = selectedSymbol;
                
                // Initialize chart and fetch data
                initTradingViewWidget(selectedSymbol);
                fetchTechnicalIndicators(selectedSymbol);
            });

            // Remove default Go button click handler
            document.getElementById('symbolSearchBtn').style.display = 'none';
        }

        // Add loader styles
        const style = document.createElement('style');
        style.textContent = `
            .loader {
                border: 3px solid rgba(255, 255, 255, 0.1);
                border-radius: 50%;
                border-top: 3px solid #00d4ff;
                width: 24px;
                height: 24px;
                animation: spin 1s linear infinite;
                margin: 20px auto;
            }

            .loading-text {
                text-align: center;
                color: #00d4ff;
                font-size: 0.9em;
                margin: 10px 0;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .empty-state {
                color: #888;
                font-size: 0.9em;
                text-align: center;
                padding: 20px;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>